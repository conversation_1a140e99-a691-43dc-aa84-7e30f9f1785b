[package]
name = "solana_price_watcher"
version = "0.1.0"
edition = "2021"
default-run = "solana_price_watcher"

[dependencies]
anyhow = "1.0.62"
backoff = { version = "0.4.0", features = ["tokio"] }
base64 = "0.22.1"
borsh = "1.5.7"
bs58 = "0.5.1"
clap = { version = "4.3.0", features = ["derive"] }
env_logger = "0.11.3"
futures = "0.3.24"
hex = "0.4.3"
log = "0.4.17"
reqwest = { version = "0.11", features = ["json"] }
rustls = "0.21"
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.135"
solana-sdk = "2.3.1"
tokio = { version = "1.21.2", features = ["rt-multi-thread", "fs"] }
tokio-rustls = "0.24.1"
toml = "0.8.23"
tonic = "0.12.1"
url = "2.5.4"
webpki-roots = "1.0.1"
yellowstone-grpc-client = "8.0.0"
yellowstone-grpc-proto = { version = "8.0.0", default-features = false, features = ["plugin"] }
pumpfun_amm_interface = { path = "./parsers/pumpfun_amm_interface", features = ["serde"] }

sled = "0.34"
solana-transaction-status = "2.3.3"

[[bin]]
name = "solana_price_watcher"
path = "src/main.rs"

[[bin]]
name = "db_export"
path = "src/bin/db_export.rs"
