use anyhow::Result;
use log::{debug, info};
use serde::{Deserialize, Serialize};
use sled::Db;
use std::path::Path;

use crate::services::token_discovery_service::DiscoveredToken;
use crate::services::cpi_pool_extractor::CpiPoolInfo;
use crate::pool::token_pool_finder::PoolType;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

/// 代币数据库操作类
/// 负责发现的代币信息的持久化存储和检索
pub struct TokenDatabase {
    db: Db,
}

/// 数据库中存储的发现代币信息
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StoredDiscoveredToken {
    pub mint_address: String,
    pub signature: String,
    pub slot: u64,
    pub block_time: Option<i64>,
    pub discovered_at: u64, // 发现时间戳
    /// 从CPI日志中提取的池子信息
    pub pool_info: Option<StoredCpiPoolInfo>,
}

/// 数据库中存储的CPI池子信息
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StoredCpiPoolInfo {
    pub pool_address: String,
    pub token_address: String,
    pub signature: String,
    pub extracted_at: u64,
    pub pool_type: String, // 存储为字符串形式
}

impl TokenDatabase {
    /// 创建新的代币数据库实例
    /// 
    /// # 参数
    /// * `db_path` - 数据库文件路径
    /// 
    /// # 返回值
    /// * `Result<Self>` - 数据库实例
    pub fn new<P: AsRef<Path>>(db_path: P) -> Result<Self> {
        // 确保数据库目录存在
        if let Some(parent) = db_path.as_ref().parent() {
            std::fs::create_dir_all(parent)?;
        }

        let db = sled::open(db_path)?;
        info!("✅ 代币数据库初始化成功");
        
        Ok(Self { db })
    }

    /// 保存发现的代币到数据库
    /// 
    /// # 参数
    /// * `token` - 发现的代币信息
    /// 
    /// # 返回值
    /// * `Result<()>` - 操作结果
    pub fn save_discovered_token(&self, token: &DiscoveredToken) -> Result<()> {
        debug!("💾 保存发现的代币到数据库: mint={}, signature={}", 
               token.mint_address, token.signature);

        // 转换为可序列化的格式
        let stored_token = StoredDiscoveredToken {
            mint_address: token.mint_address.clone(),
            signature: token.signature.clone(),
            slot: token.slot,
            block_time: token.block_time,
            discovered_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            pool_info: token.pool_info.as_ref().map(|pool| StoredCpiPoolInfo {
                pool_address: pool.pool_address.to_string(),
                token_address: pool.token_address.to_string(),
                signature: pool.signature.clone(),
                extracted_at: pool.extracted_at,
                pool_type: format!("{:?}", pool.pool_type),
            }),
        };

        // 使用 mint_address 作为主键
        let serialized = serde_json::to_vec(&stored_token)?;
        self.db.insert(token.mint_address.as_bytes(), serialized)?;
        self.db.flush()?;

        info!("✅ 发现的代币已保存到数据库: {}", token.mint_address);
        Ok(())
    }

    /// 从数据库加载发现的代币信息
    /// 
    /// # 参数
    /// * `mint_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<Option<DiscoveredToken>>` - 发现的代币信息（如果存在）
    pub fn load_discovered_token(&self, mint_address: &str) -> Result<Option<DiscoveredToken>> {
        debug!("🔍 从数据库加载发现的代币: mint={}", mint_address);

        if let Some(data) = self.db.get(mint_address.as_bytes())? {
            let stored_token: StoredDiscoveredToken = serde_json::from_slice(&data)?;
            
            let token = DiscoveredToken {
                mint_address: stored_token.mint_address,
                signature: stored_token.signature,
                slot: stored_token.slot,
                block_time: stored_token.block_time,
                pool_info: stored_token.pool_info.and_then(|pool| {
                    // 尝试解析 Pubkey，如果失败则返回 None
                    let pool_address = Pubkey::from_str(&pool.pool_address).ok()?;
                    let token_address = Pubkey::from_str(&pool.token_address).ok()?;
                    let pool_type = match pool.pool_type.as_str() {
                        "PumpFun" => PoolType::PumpFun,
                        _ => PoolType::PumpFun, // 默认为 PumpFun
                    };

                    Some(CpiPoolInfo {
                        pool_address,
                        token_address,
                        signature: pool.signature,
                        extracted_at: pool.extracted_at,
                        pool_type,
                    })
                }),
            };

            debug!("✅ 从数据库加载到发现的代币: {}", mint_address);
            Ok(Some(token))
        } else {
            debug!("❌ 数据库中未找到发现的代币: {}", mint_address);
            Ok(None)
        }
    }

    /// 检查数据库中是否存在指定代币
    /// 
    /// # 参数
    /// * `mint_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<bool>` - 是否存在
    pub fn contains_token(&self, mint_address: &str) -> Result<bool> {
        Ok(self.db.contains_key(mint_address.as_bytes())?)
    }

    /// 获取数据库中存储的所有发现的代币
    /// 
    /// # 返回值
    /// * `Result<Vec<DiscoveredToken>>` - 所有发现的代币列表
    pub fn get_all_discovered_tokens(&self) -> Result<Vec<DiscoveredToken>> {
        let mut tokens = Vec::new();
        
        for result in self.db.iter() {
            let (_, value) = result?;
            let stored_token: StoredDiscoveredToken = serde_json::from_slice(&value)?;
            
            let token = DiscoveredToken {
                mint_address: stored_token.mint_address,
                signature: stored_token.signature,
                slot: stored_token.slot,
                block_time: stored_token.block_time,
                pool_info: stored_token.pool_info.and_then(|pool| {
                    // 尝试解析 Pubkey，如果失败则跳过这个记录
                    let pool_address = Pubkey::from_str(&pool.pool_address).ok()?;
                    let token_address = Pubkey::from_str(&pool.token_address).ok()?;
                    let pool_type = match pool.pool_type.as_str() {
                        "PumpFun" => PoolType::PumpFun,
                        _ => PoolType::PumpFun, // 默认为 PumpFun
                    };

                    Some(CpiPoolInfo {
                        pool_address,
                        token_address,
                        signature: pool.signature,
                        extracted_at: pool.extracted_at,
                        pool_type,
                    })
                }),
            };
            
            tokens.push(token);
        }
        
        // 按发现时间排序（最新的在前）
        tokens.sort_by(|a, b| b.slot.cmp(&a.slot));
        
        debug!("📋 数据库中共有 {} 个发现的代币", tokens.len());
        Ok(tokens)
    }

    /// 获取最近发现的代币（限制数量）
    /// 
    /// # 参数
    /// * `limit` - 限制数量
    /// 
    /// # 返回值
    /// * `Result<Vec<DiscoveredToken>>` - 最近发现的代币列表
    pub fn get_recent_discovered_tokens(&self, limit: usize) -> Result<Vec<DiscoveredToken>> {
        let mut tokens = self.get_all_discovered_tokens()?;
        tokens.truncate(limit);
        Ok(tokens)
    }

    /// 删除指定代币
    /// 
    /// # 参数
    /// * `mint_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<bool>` - 是否删除成功（true表示存在并删除，false表示不存在）
    pub fn remove_token(&self, mint_address: &str) -> Result<bool> {
        debug!("🗑️ 从数据库删除发现的代币: {}", mint_address);
        
        let existed = self.db.remove(mint_address.as_bytes())?.is_some();
        self.db.flush()?;
        
        if existed {
            info!("✅ 已删除发现的代币: {}", mint_address);
        } else {
            debug!("❌ 发现的代币不存在: {}", mint_address);
        }
        
        Ok(existed)
    }

    /// 清空数据库
    /// 
    /// # 返回值
    /// * `Result<()>` - 操作结果
    pub fn clear(&self) -> Result<()> {
        info!("🧹 清空代币数据库");
        self.db.clear()?;
        self.db.flush()?;
        info!("✅ 代币数据库已清空");
        Ok(())
    }

    /// 获取数据库统计信息
    /// 
    /// # 返回值
    /// * `Result<TokenDatabaseStats>` - 数据库统计信息
    pub fn get_stats(&self) -> Result<TokenDatabaseStats> {
        let token_count = self.db.len();
        let size_on_disk = self.db.size_on_disk()?;
        
        Ok(TokenDatabaseStats {
            token_count,
            size_on_disk,
        })
    }
}

/// 代币数据库统计信息
#[derive(Debug)]
pub struct TokenDatabaseStats {
    /// 存储的代币数量
    pub token_count: usize,
    /// 磁盘占用大小（字节）
    pub size_on_disk: u64,
}

impl std::fmt::Display for TokenDatabaseStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "代币数据库统计: {} 个代币, 磁盘占用: {} bytes",
               self.token_count, self.size_on_disk)
    }
}
