use anyhow::{Result, anyhow};
use log::{debug, info, warn};
use serde::{Deserialize, Serialize};
use sled::Db;
use std::path::Path;

use crate::pool::token_pool_finder::{PoolDiscoveryResult, PoolSearchResult};

/// 池子数据库操作类
/// 负责池子信息的持久化存储和检索
pub struct PoolDatabase {
    db: Db,
}

/// 数据库中存储的池子发现结果
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StoredPoolDiscoveryResult {
    pub token_address: String,
    pub pools: Vec<StoredPoolSearchResult>,
    pub discovered_at: u64,
}

/// 数据库中存储的池子搜索结果
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StoredPoolSearchResult {
    pub pool_address: String,
    pub program_id: String,
    pub base_mint: String,
    pub quote_mint: String,
    pub pool_type: String, // 存储为字符串以便序列化
}

impl PoolDatabase {
    /// 创建新的池子数据库实例
    /// 
    /// # 参数
    /// * `db_path` - 数据库文件路径
    /// 
    /// # 返回值
    /// * `Result<Self>` - 数据库实例
    pub fn new<P: AsRef<Path>>(db_path: P) -> Result<Self> {
        // 确保数据库目录存在
        if let Some(parent) = db_path.as_ref().parent() {
            std::fs::create_dir_all(parent)?;
        }

        let db = sled::open(db_path)?;
        info!("✅ 池子数据库初始化成功");
        
        Ok(Self { db })
    }

    /// 保存池子发现结果到数据库
    /// 
    /// # 参数
    /// * `result` - 池子发现结果
    /// 
    /// # 返回值
    /// * `Result<()>` - 操作结果
    pub fn save_pool_discovery_result(&self, result: &PoolDiscoveryResult) -> Result<()> {
        debug!("💾 保存池子发现结果到数据库: token={}, pools={}", 
               result.token_address, result.pools.len());

        // 转换为可序列化的格式
        let stored_result = StoredPoolDiscoveryResult {
            token_address: result.token_address.clone(),
            pools: result.pools.iter().map(|pool| StoredPoolSearchResult {
                pool_address: pool.pool_address.to_string(),
                program_id: pool.program_id.to_string(),
                base_mint: pool.base_mint.to_string(),
                quote_mint: pool.quote_mint.to_string(),
                pool_type: format!("{:?}", pool.pool_type), // 转换为字符串
            }).collect(),
            discovered_at: result.discovered_at,
        };

        // 序列化并存储
        let serialized = serde_json::to_vec(&stored_result)?;
        self.db.insert(result.token_address.as_bytes(), serialized)?;
        self.db.flush()?;

        debug!("✅ 池子发现结果已保存到数据库");
        Ok(())
    }

    /// 从数据库加载池子发现结果
    /// 
    /// # 参数
    /// * `token_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<Option<PoolDiscoveryResult>>` - 池子发现结果（如果存在）
    pub fn load_pool_discovery_result(&self, token_address: &str) -> Result<Option<PoolDiscoveryResult>> {
        debug!("🔍 从数据库加载池子发现结果: token={}", token_address);

        if let Some(data) = self.db.get(token_address.as_bytes())? {
            let stored_result: StoredPoolDiscoveryResult = serde_json::from_slice(&data)?;
            
            // 转换回原始格式
            let pools = stored_result.pools.into_iter().map(|stored_pool| -> Result<PoolSearchResult> {
                Ok(PoolSearchResult {
                    pool_address: stored_pool.pool_address.parse()
                        .map_err(|e| anyhow!("解析池子地址失败: {}", e))?,
                    program_id: stored_pool.program_id.parse()
                        .map_err(|e| anyhow!("解析程序ID失败: {}", e))?,
                    base_mint: stored_pool.base_mint.parse()
                        .map_err(|e| anyhow!("解析基础代币地址失败: {}", e))?,
                    quote_mint: stored_pool.quote_mint.parse()
                        .map_err(|e| anyhow!("解析报价代币地址失败: {}", e))?,
                    pool_type: match stored_pool.pool_type.as_str() {
                        "PumpFun" => crate::pool::token_pool_finder::PoolType::PumpFun,
                        "PumpAMM" => crate::pool::token_pool_finder::PoolType::PumpAMM,
                        _ => {
                            warn!("未知的池子类型: {}, 默认使用PumpFun", stored_pool.pool_type);
                            crate::pool::token_pool_finder::PoolType::PumpFun
                        }
                    },
                })
            }).collect::<Result<Vec<_>>>()?;

            let result = PoolDiscoveryResult {
                token_address: stored_result.token_address,
                pools,
                discovered_at: stored_result.discovered_at,
            };

            debug!("✅ 从数据库加载到 {} 个池子", result.pools.len());
            Ok(Some(result))
        } else {
            debug!("❌ 数据库中未找到token对应的池子: {}", token_address);
            Ok(None)
        }
    }

    /// 检查数据库中是否存在指定token的池子信息
    /// 
    /// # 参数
    /// * `token_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<bool>` - 是否存在
    pub fn contains_token(&self, token_address: &str) -> Result<bool> {
        Ok(self.db.contains_key(token_address.as_bytes())?)
    }

    /// 删除指定token的池子信息
    /// 
    /// # 参数
    /// * `token_address` - 代币地址
    /// 
    /// # 返回值
    /// * `Result<bool>` - 是否删除成功（true表示存在并删除，false表示不存在）
    pub fn remove_token(&self, token_address: &str) -> Result<bool> {
        debug!("🗑️ 从数据库删除token池子信息: {}", token_address);
        
        let existed = self.db.remove(token_address.as_bytes())?.is_some();
        self.db.flush()?;
        
        if existed {
            debug!("✅ 已删除token池子信息: {}", token_address);
        } else {
            debug!("❌ token池子信息不存在: {}", token_address);
        }
        
        Ok(existed)
    }

    /// 获取数据库中存储的所有token地址
    ///
    /// # 返回值
    /// * `Result<Vec<String>>` - 所有token地址列表
    pub fn get_all_tokens(&self) -> Result<Vec<String>> {
        let mut tokens = Vec::new();

        for result in self.db.iter() {
            let (key, _) = result?;
            let token_address = String::from_utf8(key.to_vec())?;
            tokens.push(token_address);
        }

        debug!("📋 数据库中共有 {} 个token", tokens.len());
        Ok(tokens)
    }



    /// 清空数据库
    /// 
    /// # 返回值
    /// * `Result<()>` - 操作结果
    pub fn clear(&self) -> Result<()> {
        info!("🧹 清空池子数据库");
        self.db.clear()?;
        self.db.flush()?;
        info!("✅ 池子数据库已清空");
        Ok(())
    }

    /// 获取所有已发现的代币地址列表
    ///
    /// # 返回值
    /// * `Result<Vec<String>>` - 所有代币地址列表
    pub fn get_all_token_addresses(&self) -> Result<Vec<String>> {
        let mut token_addresses = Vec::new();

        for result in self.db.iter() {
            let (key, _) = result?;
            let token_address = String::from_utf8_lossy(&key).to_string();
            token_addresses.push(token_address);
        }

        debug!("📋 数据库中共有 {} 个代币", token_addresses.len());
        Ok(token_addresses)
    }

    /// 获取数据库统计信息
    ///
    /// # 返回值
    /// * `Result<DatabaseStats>` - 数据库统计信息
    pub fn get_stats(&self) -> Result<DatabaseStats> {
        let token_count = self.db.len();
        let size_on_disk = self.db.size_on_disk()?;

        Ok(DatabaseStats {
            token_count,
            size_on_disk,
        })
    }
}

/// 数据库统计信息
#[derive(Debug)]
pub struct DatabaseStats {
    /// 存储的token数量
    pub token_count: usize,
    /// 磁盘占用大小（字节）
    pub size_on_disk: u64,
}

impl std::fmt::Display for DatabaseStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "数据库统计: {} 个token, 磁盘占用: {} bytes",
               self.token_count, self.size_on_disk)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::{SystemTime, UNIX_EPOCH};
    use solana_sdk::pubkey::Pubkey;
    use crate::pool::token_pool_finder::{PoolType, PoolSearchResult, PoolDiscoveryResult};

    /// 查看实际数据库内容的测试函数
    /// 这个测试会连接到实际的数据库文件并显示其内容
    #[test]
    #[ignore] // 使用 #[ignore] 标记，需要手动运行
    fn test_inspect_actual_database() {
        // 连接到实际的数据库文件
        let db_path = "db/pool_cache.db";

        // 检查数据库文件是否存在
        if !std::path::Path::new(db_path).exists() {
            println!("❌ 数据库文件不存在: {}", db_path);
            return;
        }

        let db = match PoolDatabase::new(db_path) {
            Ok(db) => db,
            Err(e) => {
                println!("❌ 无法打开数据库: {}", e);
                return;
            }
        };

        println!("=== 数据库原始存储结构检查 ===");

        // 遍历数据库中的所有键值对
        for result in db.db.iter() {
            match result {
                Ok((key, value)) => {
                    let key_str = String::from_utf8_lossy(&key);
                    println!("Key: {}", key_str);
                    println!("Key (hex): {}", hex::encode(&key));
                    println!("Value length: {} bytes", value.len());
                    println!("Value (hex): {}", hex::encode(&value));
                    println!("---");
                }
                Err(e) => {
                    println!("读取键值对失败: {}", e);
                }
            }
        }

        // 显示数据库统计
        println!("=== 数据库统计 ===");
        println!("数据库路径: {}", db_path);
        if let Ok(stats) = db.get_stats() {
            println!("总token数: {}", stats.token_count);
            println!("磁盘占用: {} bytes", stats.size_on_disk);
        }
    }
}
