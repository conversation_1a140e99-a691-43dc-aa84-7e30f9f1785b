use anyhow::Result;
use clap::Parser;
use log::{info, warn};

mod client;
mod config;
mod constants;
mod db;
mod pool;
mod pool_parser;
mod utils;
mod handlers;
mod pojo;
mod parsers;
mod services;
mod token_manager;
mod controllers;

use config::Config;
use client::http_client::HttpRpcClient;
use controllers::PoolDiscoveryController;
use token_manager::TokenPoolManager;
use services::TokenDiscoveryService;
use std::sync::Arc;

/// 命令行参数
#[derive(Debug, Clone, Parser)]
#[clap(author, version, about)]
struct Args {
    /// 配置文件路径
    #[clap(short, long, default_value = "config.toml")]
    config: String,

    /// 运行模式
    #[clap(short, long, default_value = "price-monitor")]
    mode: String,

    /// 要发现池子的token地址列表（用逗号分隔）
    #[clap(short, long)]
    tokens: Option<String>,


}

#[tokio::main]
async fn main() -> Result<()> {
    // 解析命令行参数
    let args = Args::parse();

    // 加载配置
    let config = Config::load_from_file(&args.config)?;

    // 初始化日志
    let log_level = match config.logging.level.as_str() {
        "trace" => log::LevelFilter::Trace,
        "debug" => log::LevelFilter::Debug,
        "info" => log::LevelFilter::Info,
        "warn" => log::LevelFilter::Warn,
        "error" => log::LevelFilter::Error,
        _ => log::LevelFilter::Debug,
    };

    env_logger::Builder::from_default_env()
        .filter_level(log_level)
        .init();

    info!("启动 Solana Pump.fun 工具");
    info!("配置文件: {}", args.config);
    info!("运行模式: {}", args.mode);



    // 配置已从文件加载，无需额外验证

    // 根据运行模式执行不同的功能
    match args.mode.as_str() {
        "pool-discovery" => {
            // 池子发现模式
            run_pool_discovery_mode(&args, &config).await?;
        }
        "token-discovery" => {
            // 代币发现模式
            run_token_discovery_mode(&config).await?;
        }
        "price-monitor" => {
            // 价格监控模式（原有功能）
            run_price_monitor_mode(&config).await?;
        }
        "price-monitor-db" => {
            // 价格监控模式（数据库和配置文件代币）
            run_price_monitor_db_mode(&config).await?;
        }
        _ => {
            return Err(anyhow::anyhow!("不支持的运行模式: {}。支持的模式: pool-discovery, token-discovery, price-monitor, price-monitor-db", args.mode));
        }
    }

    Ok(())
}

/// 运行池子发现模式
async fn run_pool_discovery_mode(args: &Args, config: &Config) -> Result<()> {
    info!("🔍 启动池子发现模式");

    // 获取要发现的token列表
    let token_list = if let Some(tokens_str) = &args.tokens {
        // 从命令行参数获取
        tokens_str
            .split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect::<Vec<String>>()
    } else if !config.monitoring.token_addresses.is_empty() {
        // 从配置文件获取
        config.monitoring.token_addresses.clone()
    } else {
        return Err(anyhow::anyhow!("请通过 --tokens 参数或配置文件指定要发现池子的token地址"));
    };

    if token_list.is_empty() {
        return Err(anyhow::anyhow!("token列表为空"));
    }

    info!("待发现池子的token列表 ({} 个):", token_list.len());
    for (i, token) in token_list.iter().enumerate() {
        info!("  {}. {}", i + 1, token);
    }

    // 创建池子发现控制器
    let mut controller = PoolDiscoveryController::new(config.clone(), token_list).await?;

    // 启动发现流程
    let result = controller.start_discovery().await?;

    // 打印结果
    result.print_summary();

    // 详细输出每个token的发现结果
    info!("📋 详细发现结果:");
    for (token, pools) in &result.token_pool_mapping {
        if pools.is_empty() {
            warn!("  ❌ {}: 未发现池子", token);
        } else {
            info!("  ✅ {}: 发现 {} 个池子", token, pools.len());
            for (i, pool) in pools.iter().enumerate() {
                info!("    {}. {}", i + 1, pool);
            }
        }
    }

    Ok(())
}

/// 运行代币发现模式
async fn run_token_discovery_mode(config: &Config) -> Result<()> {
    info!("🔍 启动代币发现模式");

    // 初始化数据库（如果启用）
    if config.database.enabled {
        info!("🗄️ 数据库已启用，路径: {}", config.database.path);
        // 确保数据库目录存在
        if let Some(parent) = std::path::Path::new(&config.database.path).parent() {
            std::fs::create_dir_all(parent)?;
            info!("✅ 数据库目录已创建: {:?}", parent);
        }
    } else {
        info!("💾 数据库未启用，仅使用内存缓存");
    }

    // 创建HTTP RPC客户端
    let rpc_client = HttpRpcClient::new(config.rpc.clone())?;

    // 创建带自动池子发现功能的Token池子管理器
    let token_manager = Arc::new(TokenPoolManager::from_config_with_discovery(config, rpc_client.clone())?);
    token_manager.initialize().await?;

    // 创建代币发现服务
    let discovery_service = if config.database.enabled {
        // 创建代币数据库路径
        let token_db_path = format!("{}/tokens", config.database.path);
        TokenDiscoveryService::with_database(
            config.grpc.clone(),
            Arc::clone(&token_manager),
            rpc_client,
            &token_db_path
        )?
    } else {
        TokenDiscoveryService::new(config.grpc.clone(), Arc::clone(&token_manager), rpc_client)
    };

    info!("🚀 启动代币发现服务...");

    // 启动代币发现（这是一个长期运行的服务）
    discovery_service.start_discovery().await?;

    Ok(())
}

/// 运行价格监控模式（使用TokenPoolManager）
async fn run_price_monitor_mode(config: &Config) -> Result<()> {
    info!("📊 启动价格监控模式");

    // 初始化数据库（如果启用）
    if config.database.enabled {
        info!("🗄️ 数据库已启用，路径: {}", config.database.path);
        // 确保数据库目录存在
        if let Some(parent) = std::path::Path::new(&config.database.path).parent() {
            std::fs::create_dir_all(parent)?;
            info!("✅ 数据库目录已创建: {:?}", parent);
        }
    } else {
        info!("💾 数据库未启用，仅使用内存缓存");
    }

    // 创建Token池子管理器
    let token_manager = TokenPoolManager::from_config(config);
    token_manager.initialize().await?;

    // 创建 RPC 客户端
    let rpc_client = HttpRpcClient::new(config.rpc.clone())?;

    // 获取要监控的token列表
    let token_addresses = token_manager.get_configured_tokens();
    if token_addresses.is_empty() {
        return Err(anyhow::anyhow!("配置中没有指定要监控的token地址"));
    }

    info!("要监控的token列表 ({} 个):", token_addresses.len());
    for (i, token) in token_addresses.iter().enumerate() {
        info!("  {}. {}", i + 1, token);
    }

    // 执行主要功能：收集所有池子并统一监控
    let all_pools = collect_all_pools_for_tokens(token_addresses.to_vec(), &rpc_client, config, &token_manager).await?;

    if all_pools.is_empty() {
        warn!("没有找到任何池子进行监控");
        return Ok(());
    }

    info!("总共收集到 {} 个池子，开始统一监控", all_pools.len());

    // 统一监控所有池子
    monitor_all_pools_simultaneously(all_pools, config).await?;

    Ok(())
}

/// 收集所有代币对应的池子
async fn collect_all_pools_for_tokens(
    token_addresses: Vec<String>,
    rpc_client: &HttpRpcClient,
    config: &Config,
    token_manager: &TokenPoolManager,
) -> Result<Vec<pool::PoolSearchResult>> {
    let mut all_pools = Vec::new();

    for token_address in token_addresses {
        info!("=== 开始处理token: {} ===", token_address);

        // 1. 先检查TokenPoolManager中是否已有池子信息
        let pools = if let Some(existing_pools) = token_manager.get_pools_for_token(&token_address).await {
            info!("✅ 从TokenPoolManager中找到 {} 个已知池子", existing_pools.len());
            existing_pools
        } else {
            // 2. 如果没有，则使用TokenPoolFinder查找
            info!("🔍 TokenPoolManager中没有池子信息，开始查找...");
            let mut token_finder = if config.database.enabled {
                info!("📁 启用数据库缓存: {}", config.database.path);
                pool::TokenPoolFinder::with_database(&config.database.path)?
            } else {
                info!("💾 使用内存缓存");
                pool::TokenPoolFinder::new()
            };
            let discovery_result = token_finder.find_pools_by_token(&token_address, rpc_client).await?;

            if discovery_result.pools.is_empty() {
                warn!("未找到token {} 对应的池子", token_address);
                continue;
            }

            // 将发现的池子保存到TokenPoolManager中
            token_manager.mark_token_processed(&token_address, discovery_result.pools.clone()).await?;
            discovery_result.pools
        };

        info!("找到 {} 个池子:", pools.len());
        for (i, pool) in pools.iter().enumerate() {
            info!("  池子 {}: {} (类型: {:?})", i + 1, pool.pool_address, pool.pool_type);
        }

        // 将池子添加到总列表中
        all_pools.extend(pools);
    }

    Ok(all_pools)
}

/// 统一监控所有池子
async fn monitor_all_pools_simultaneously(
    pools: Vec<pool::PoolSearchResult>,
    config: &Config,
) -> Result<()> {
    info!("=== 开始统一监控 {} 个池子 ===", pools.len());

    // 按池子类型分组
    let mut pump_amm_pools = Vec::new();
    let mut other_pools = Vec::new();

    for pool in pools {
        match &pool.pool_type {
            pool::PoolType::PumpAMM => {
                pump_amm_pools.push(pool);
            }
            _ => {
                other_pools.push(pool);
            }
        }
    }

    info!("池子分类统计:");
    info!("  PumpAMM 池子: {} 个", pump_amm_pools.len());
    info!("  其他类型池子: {} 个", other_pools.len());

    // 如果有PumpAMM池子，使用gRPC统一监控
    if !pump_amm_pools.is_empty() {
        info!("🚀 启动 PumpAMM 池子统一 gRPC 监控");
        monitor_pump_amm_pools_with_grpc(pump_amm_pools, config).await?;
    }

    Ok(())
}

/// 使用 gRPC 统一监控多个 PumpAMM 池子
async fn monitor_pump_amm_pools_with_grpc(
    pools: Vec<pool::PoolSearchResult>,
    config: &Config,
) -> Result<()> {
    info!("=== 启动 PumpFun AMM 多池子统一 gRPC 监控 ===");
    info!("监控池子数量: {}", pools.len());

    // 打印所有要监控的池子信息
    for (i, pool) in pools.iter().enumerate() {
        info!("池子 {}: {}", i + 1, pool.pool_address);
        info!("  基础代币: {}", pool.base_mint);
        info!("  报价代币: {}", pool.quote_mint);
        info!("  程序ID: {}", pool.program_id);
    }

    // 创建gRPC客户端
    use crate::client::grpc::YellowstoneGrpcClient;
    let grpc_client = YellowstoneGrpcClient::new(config.grpc.clone())?;

    info!("🔗 连接到 gRPC 服务器: {}", config.grpc.endpoint);

    // 准备要监控的程序ID列表 - 只监控 Pump AMM 程序的交易
    let program_ids = vec![
        constants::programs::PUMP_AMM_PROGRAM_ID.to_string(), // 从常量获取 Pump AMM 程序ID
    ];

    // 提取所有池子地址
    let pool_addresses: Vec<String> = pools.iter()
        .map(|pool| pool.pool_address.to_string())
        .collect();

    info!("📡 开始订阅交易更新...");
    info!("监控程序ID: {:?}", program_ids);
    info!("监控池子地址: {:?}", pool_addresses);

    // 启动交易流监听，监控涉及所有池子的交易
    grpc_client.start_transaction_stream_for_multiple_pools(program_ids, pool_addresses).await?;

    Ok(())
}

/// 监控单个池子的价格
async fn monitor_pool_price(
    pool_result: &pool::PoolSearchResult,
    rpc_client: &HttpRpcClient,
    config: &Config,
) -> Result<()> {
    info!("开始实时价格监控，池子类型: {:?}", pool_result.pool_type);

    match &pool_result.pool_type {
        pool::PoolType::PumpAMM => {
            info!("🚀 检测到 PumpFun AMM 池子，使用 gRPC 流监控");
            monitor_pump_amm_pool_with_grpc(pool_result, config).await?;
        }
        pool::PoolType::PumpFun => {
            info!("📊 检测到 PumpFun 原始池子，使用 HTTP 轮询监控");
            monitor_pool_with_http_polling(pool_result, rpc_client, config).await?;
        }
        pool::PoolType::Other(pool_type) => {
            warn!("⚠️  未知池子类型: {}，使用默认 HTTP 轮询监控", pool_type);
            monitor_pool_with_http_polling(pool_result, rpc_client, config).await?;
        }
    }

    Ok(())
}

/// 使用 gRPC 流监控 PumpFun AMM 池子
async fn monitor_pump_amm_pool_with_grpc(
    pool_result: &pool::PoolSearchResult,
    config: &Config,
) -> Result<()> {
    info!("=== 启动 PumpFun AMM gRPC 流监控 ===");
    info!("池子地址: {}", pool_result.pool_address);
    info!("基础代币: {}", pool_result.base_mint);
    info!("报价代币: {}", pool_result.quote_mint);
    info!("程序ID: {}", pool_result.program_id);

    // 创建gRPC客户端
    use crate::client::grpc::YellowstoneGrpcClient;
    let grpc_client = YellowstoneGrpcClient::new(config.grpc.clone())?;

    info!("🔗 连接到 gRPC 服务器: {}", config.grpc.endpoint);

    // 准备要监控的程序ID列表 - 只监控 Pump AMM 程序的交易
    let program_ids = vec![
        constants::programs::PUMP_AMM_PROGRAM_ID.to_string(), // 从常量获取 Pump AMM 程序ID
    ];

    info!("📡 开始订阅交易更新...");
    info!("监控程序ID: {:?}", program_ids);
    info!("特定池子地址: {}", pool_result.pool_address);

    // 启动交易流监听，监控涉及特定池子的交易
    grpc_client.start_transaction_stream_for_pool(program_ids, pool_result.pool_address.to_string()).await?;

    Ok(())
}

/// 使用 HTTP 轮询监控池子
async fn monitor_pool_with_http_polling(
    pool_result: &pool::PoolSearchResult,
    rpc_client: &HttpRpcClient,
    config: &Config,
) -> Result<()> {
    info!("=== 启动 HTTP 轮询监控 ===");
    info!("监控间隔: {}秒", config.monitoring.monitor_interval_seconds);

    // 创建AMM价格监控器
    let amm_monitor = pool::AmmPriceMonitor::new(rpc_client.clone());

    // 开始监控
    amm_monitor.start_monitoring(pool_result, config.monitoring.monitor_interval_seconds).await?;

    Ok(())
}

/// 运行价格监控模式（数据库和配置文件代币）
async fn run_price_monitor_db_mode(config: &Config) -> Result<()> {
    info!("📊 启动价格监控模式（数据库和配置文件代币）");

    // 初始化数据库（如果启用）
    if config.database.enabled {
        info!("🗄️ 数据库已启用，路径: {}", config.database.path);
        // 确保数据库目录存在
        if let Some(parent) = std::path::Path::new(&config.database.path).parent() {
            std::fs::create_dir_all(parent)?;
            info!("✅ 数据库目录已创建: {:?}", parent);
        }
    } else {
        return Err(anyhow::anyhow!("价格监控模式（数据库）需要启用数据库配置"));
    }

    // 创建Token池子管理器
    let token_manager = TokenPoolManager::from_config(config);
    token_manager.initialize().await?;

    // 创建 RPC 客户端
    let rpc_client = HttpRpcClient::new(config.rpc.clone())?;

    // 获取要监控的token列表（配置文件 + 数据库）
    let mut token_addresses = get_tokens_for_monitoring(config).await?;

    // #debug-start#
    // 调试模式下限制为10个代币
    if token_addresses.len() > 10 {
        info!("⚠️  调试模式：限制监控代币数量从 {} 个减少到 10 个", token_addresses.len());
        token_addresses.truncate(10);
    }
    // #debug-end#

    if token_addresses.is_empty() {
        return Err(anyhow::anyhow!("没有找到要监控的token地址"));
    }

    info!("要监控的token列表 ({} 个):", token_addresses.len());
    for (i, token) in token_addresses.iter().enumerate() {
        info!("  {}. {}", i + 1, token);
    }

    // 执行主要功能：收集所有池子并统一监控
    let all_pools = collect_all_pools_for_tokens(token_addresses, &rpc_client, config, &token_manager).await?;

    if all_pools.is_empty() {
        warn!("没有找到任何池子进行监控");
        return Ok(());
    }

    info!("总共收集到 {} 个池子，开始统一监控", all_pools.len());

    // 统一监控所有池子
    monitor_all_pools_simultaneously(all_pools, config).await?;

    Ok(())
}

/// 获取要监控的代币列表（配置文件 + 数据库）
async fn get_tokens_for_monitoring(config: &Config) -> Result<Vec<String>> {
    let mut token_addresses = Vec::new();

    // 1. 从配置文件获取代币
    info!("📋 从配置文件获取代币列表");
    for token in &config.monitoring.token_addresses {
        token_addresses.push(token.clone());
        info!("  配置文件代币: {}", token);
    }

    // 2. 从数据库获取发现的代币
    if config.database.enabled {
        info!("🗄️ 从数据库获取发现的代币列表");

        // 创建代币数据库连接
        use crate::db::token_db::TokenDatabase;
        // 代币数据库路径是在主数据库路径下的tokens子目录
        let token_db_path = format!("{}/tokens", config.database.path);
        let token_db = TokenDatabase::new(&token_db_path)?;

        // 获取所有发现的代币
        let discovered_tokens = token_db.get_all_discovered_tokens()?;
        info!("数据库中发现 {} 个代币", discovered_tokens.len());

        for discovered_token in discovered_tokens {
            // 避免重复添加
            if !token_addresses.contains(&discovered_token.mint_address) {
                token_addresses.push(discovered_token.mint_address.clone());
                info!("  数据库代币: {}", discovered_token.mint_address);
            } else {
                info!("  跳过重复代币: {}", discovered_token.mint_address);
            }
        }
    } else {
        info!("💾 数据库未启用，仅使用配置文件代币");
    }

    info!("总计获取到 {} 个唯一代币", token_addresses.len());
    Ok(token_addresses)
}


