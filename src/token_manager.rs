use std::collections::{HashSet, HashMap};
use std::sync::Arc;
use tokio::sync::Mutex;
use anyhow::Result;
use log::{info, debug, warn};
use serde::{Deserialize, Serialize};

use crate::config::Config;
use crate::pool::token_pool_finder::{PoolSearchResult, TokenPoolFinder};
use crate::client::http_client::HttpRpcClient;

/// Token池子管理器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenPoolManagerConfig {
    /// 要管理的token地址列表
    pub token_addresses: Vec<String>,
}

impl From<&Config> for TokenPoolManagerConfig {
    fn from(config: &Config) -> Self {
        Self {
            token_addresses: config.monitoring.token_addresses.clone(),
        }
    }
}

/// Token和池子关系管理器
///
/// 管理配置文件中token和池子的关系，并支持自动池子发现功能
pub struct TokenPoolManager {
    /// 配置
    config: TokenPoolManagerConfig,
    /// 待处理的token列表
    pending_tokens: Arc<Mutex<HashSet<String>>>,
    /// 已处理的token列表
    processed_tokens: Arc<Mutex<HashSet<String>>>,
    /// Token到池子的映射关系
    token_pool_mapping: Arc<Mutex<HashMap<String, Vec<PoolSearchResult>>>>,
    /// 池子发现器（可选）
    pool_finder: Option<Arc<Mutex<TokenPoolFinder>>>,
    /// RPC客户端（可选）
    rpc_client: Option<Arc<HttpRpcClient>>,
}

impl TokenPoolManager {
    /// 创建新的Token池子管理器（不带自动池子发现）
    pub fn new(config: TokenPoolManagerConfig) -> Self {
        Self {
            config,
            pending_tokens: Arc::new(Mutex::new(HashSet::new())),
            processed_tokens: Arc::new(Mutex::new(HashSet::new())),
            token_pool_mapping: Arc::new(Mutex::new(HashMap::new())),
            pool_finder: None,
            rpc_client: None,
        }
    }

    /// 创建带自动池子发现功能的Token池子管理器
    pub fn with_pool_discovery(
        config: TokenPoolManagerConfig,
        pool_finder: TokenPoolFinder,
        rpc_client: HttpRpcClient,
    ) -> Self {
        Self {
            config,
            pending_tokens: Arc::new(Mutex::new(HashSet::new())),
            processed_tokens: Arc::new(Mutex::new(HashSet::new())),
            token_pool_mapping: Arc::new(Mutex::new(HashMap::new())),
            pool_finder: Some(Arc::new(Mutex::new(pool_finder))),
            rpc_client: Some(Arc::new(rpc_client)),
        }
    }

    /// 从配置创建池子管理器（不带自动池子发现）
    pub fn from_config(config: &Config) -> Self {
        let pool_config = TokenPoolManagerConfig::from(config);
        Self::new(pool_config)
    }

    /// 从配置创建带自动池子发现功能的池子管理器
    pub fn from_config_with_discovery(config: &Config, rpc_client: HttpRpcClient) -> Result<Self> {
        let pool_config = TokenPoolManagerConfig::from(config);

        // 创建池子发现器
        let pool_finder = if config.database.enabled {
            TokenPoolFinder::with_database(&config.database.path)?
        } else {
            TokenPoolFinder::new()
        };

        Ok(Self::with_pool_discovery(pool_config, pool_finder, rpc_client))
    }

    /// 初始化管理器
    ///
    /// 将配置的token添加到待处理列表
    pub async fn initialize(&self) -> Result<()> {
        info!("🚀 初始化Token池子管理器");

        // 将配置的token添加到待处理列表
        let mut pending = self.pending_tokens.lock().await;
        for token in &self.config.token_addresses {
            pending.insert(token.clone());
            info!("添加待处理token: {}", token);
        }
        info!("初始化完成，待处理token数量: {}", pending.len());

        Ok(())
    }

    /// 添加token到待处理列表，并自动尝试发现池子
    pub async fn add_token(&self, token_address: String) -> Result<()> {
        self.add_token_with_pools(token_address, None).await
    }

    /// 添加token到待处理列表，可选择提供已知的池子信息
    /// 如果提供了池子信息，将跳过自动池子发现
    pub async fn add_token_with_pools(&self, token_address: String, known_pools: Option<Vec<PoolSearchResult>>) -> Result<()> {
        debug!("🔧 add_token_with_pools 被调用: token={}, known_pools={:?}",
               token_address, known_pools.as_ref().map(|p| p.len()));

        let mut pending = self.pending_tokens.lock().await;
        let processed = self.processed_tokens.lock().await;

        // 检查是否已经处理过
        if processed.contains(&token_address) {
            warn!("Token {} 已经处理过，跳过添加", token_address);
            return Ok(());
        }

        let is_new_token = pending.insert(token_address.clone());
        drop(pending);
        drop(processed);

        if is_new_token {
            info!("添加新token到待处理列表: {}", token_address);

            // 如果提供了已知的池子信息，直接使用
            if let Some(pools) = known_pools {
                info!("✅ 使用提供的池子信息，跳过自动发现");
                for pool in &pools {
                    info!("  - 池子地址: {}", pool.pool_address);
                }
                self.mark_token_processed(&token_address, pools).await?;
                return Ok(());
            } else {
                debug!("💡 未提供池子信息，将进行自动池子发现");
            }

            // 如果配置了自动池子发现，立即尝试发现池子
            if let (Some(pool_finder), Some(rpc_client)) = (&self.pool_finder, &self.rpc_client) {
                info!("🔍 自动触发池子发现: {}", token_address);

                let mut finder = pool_finder.lock().await;
                let client = Arc::clone(rpc_client);

                match finder.find_pools_by_token(&token_address, &client).await {
                    Ok(discovery_result) => {
                        let pools = discovery_result.pools;
                        if !pools.is_empty() {
                            info!("✅ 自动发现到 {} 个池子", pools.len());
                            for pool in &pools {
                                info!("  - 池子地址: {}", pool.pool_address);
                            }

                            // 立即标记为已处理
                            drop(finder);
                            self.mark_token_processed(&token_address, pools).await?;
                        } else {
                            warn!("⚠️ 自动池子发现未找到池子: {}", token_address);
                        }
                    }
                    Err(e) => {
                        warn!("❌ 自动池子发现失败: {}, 错误: {}", token_address, e);
                    }
                }
            }
        } else {
            debug!("Token {} 已在待处理列表中", token_address);
        }

        Ok(())
    }

    /// 标记token为已处理并从待处理列表中移除
    pub async fn mark_token_processed(&self, token_address: &str, pools: Vec<PoolSearchResult>) -> Result<()> {
        let mut pending = self.pending_tokens.lock().await;
        let mut processed = self.processed_tokens.lock().await;
        let mut mapping = self.token_pool_mapping.lock().await;

        // 从待处理列表中移除
        if pending.remove(token_address) {
            info!("从待处理列表中移除token: {}", token_address);
        }

        // 添加到已处理列表
        processed.insert(token_address.to_string());

        // 保存池子映射关系
        mapping.insert(token_address.to_string(), pools.clone());

        // 如果有池子发现器，将已知的池子信息同步到池子发现器的缓存中
        if let Some(pool_finder) = &self.pool_finder {
            let mut finder = pool_finder.lock().await;
            finder.add_known_mapping(token_address, pools.clone());
            info!("✅ 已将池子信息同步到池子发现器缓存: {}", token_address);
        }

        info!("标记token {} 为已处理，关联 {} 个池子", token_address, pools.len());
        for pool in &pools {
            info!("  - 池子地址: {}", pool.pool_address);
        }

        Ok(())
    }

    /// 获取指定token的池子列表
    pub async fn get_pools_for_token(&self, token_address: &str) -> Option<Vec<PoolSearchResult>> {
        let mapping = self.token_pool_mapping.lock().await;
        mapping.get(token_address).cloned()
    }

    /// 获取所有token的池子映射
    pub async fn get_all_token_pool_mappings(&self) -> HashMap<String, Vec<PoolSearchResult>> {
        let mapping = self.token_pool_mapping.lock().await;
        mapping.clone()
    }

    /// 获取配置的token地址列表
    pub fn get_configured_tokens(&self) -> &[String] {
        &self.config.token_addresses
    }

    /// 检查是否有指定token的池子
    pub async fn has_pools_for_token(&self, token_address: &str) -> bool {
        let mapping = self.token_pool_mapping.lock().await;
        mapping.get(token_address)
            .map(|pools| !pools.is_empty())
            .unwrap_or(false)
    }

    /// 获取当前待处理的token列表
    pub async fn get_pending_tokens(&self) -> Vec<String> {
        let pending = self.pending_tokens.lock().await;
        pending.iter().cloned().collect()
    }

    /// 获取下一个要处理的token（不移除）
    pub async fn get_next_token(&self) -> Option<String> {
        let pending = self.pending_tokens.lock().await;
        pending.iter().next().cloned()
    }

    /// 检查是否还有待处理的token
    pub async fn has_pending_tokens(&self) -> bool {
        let pending = self.pending_tokens.lock().await;
        !pending.is_empty()
    }

    /// 获取已处理的token数量
    pub async fn get_processed_count(&self) -> usize {
        let processed = self.processed_tokens.lock().await;
        processed.len()
    }

    /// 获取待处理的token数量
    pub async fn get_pending_count(&self) -> usize {
        let pending = self.pending_tokens.lock().await;
        pending.len()
    }

    /// 获取总token数量
    pub async fn get_total_count(&self) -> usize {
        let pending = self.pending_tokens.lock().await;
        let processed = self.processed_tokens.lock().await;
        pending.len() + processed.len()
    }

    /// 获取处理进度（已处理/总数）
    pub async fn get_progress(&self) -> (usize, usize) {
        let processed_count = self.get_processed_count().await;
        let total_count = self.get_total_count().await;
        (processed_count, total_count)
    }

    /// 获取指定token发现的池子地址
    pub async fn get_discovered_pools(&self, token_address: &str) -> Vec<String> {
        let mapping = self.token_pool_mapping.lock().await;
        mapping.get(token_address)
            .map(|pools| pools.iter().map(|p| p.pool_address.to_string()).collect())
            .unwrap_or_default()
    }

    /// 获取所有发现的池子地址映射
    pub async fn get_all_discovered_pools(&self) -> HashMap<String, Vec<String>> {
        let mapping = self.token_pool_mapping.lock().await;
        mapping.iter()
            .map(|(token, pools)| {
                let pool_addresses = pools.iter().map(|p| p.pool_address.to_string()).collect();
                (token.clone(), pool_addresses)
            })
            .collect()
    }

    /// 检查是否所有token都已处理完成
    pub async fn is_all_completed(&self) -> bool {
        !self.has_pending_tokens().await
    }

    /// 获取状态摘要
    pub async fn get_status_summary(&self) -> TokenManagerStatus {
        let (processed, total) = self.get_progress().await;
        let pending = self.get_pending_count().await;
        
        TokenManagerStatus {
            total_tokens: total,
            processed_tokens: processed,
            pending_tokens: pending,
            is_completed: self.is_all_completed().await,
        }
    }

    /// 清空所有数据
    pub async fn clear(&self) {
        let mut pending = self.pending_tokens.lock().await;
        let mut processed = self.processed_tokens.lock().await;
        let mut pools = self.token_pool_mapping.lock().await;

        pending.clear();
        processed.clear();
        pools.clear();

        info!("清空Token池子管理器所有数据");
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> TokenManagerStatus {
        let mapping = self.token_pool_mapping.lock().await;
        let total_tokens = self.config.token_addresses.len();
        let tokens_with_pools = mapping.len();
        let pending_count = self.get_pending_count().await;

        TokenManagerStatus {
            total_tokens,
            processed_tokens: tokens_with_pools,
            pending_tokens: pending_count,
            is_completed: pending_count == 0,
        }
    }
}

/// Token管理器状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenManagerStatus {
    pub total_tokens: usize,
    pub processed_tokens: usize,
    pub pending_tokens: usize,
    pub is_completed: bool,
}

impl Default for TokenPoolManager {
    fn default() -> Self {
        Self::new(TokenPoolManagerConfig {
            token_addresses: Vec::new(),
        })
    }
}
