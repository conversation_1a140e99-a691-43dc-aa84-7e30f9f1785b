/// Solana 程序常量
pub mod programs {
    /// Pump AMM 程序 ID (用于池子检测)
    pub const PUMP_AMM_PROGRAM_ID: &str = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";

    /// Pump.fun 原始程序 ID (用于代币创建和交易)
    pub const PUMP_FUN_PROGRAM_ID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";

    /// Pump.fun MINT_AUTHORITY 程序
    pub const MINT_AUTHORITY: &str = "TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM";

    /// SPL Token 程序 ID
    pub const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
}

/// CPI日志标识符常量
pub mod cpi {
    /// Anchor CPI日志标识符 (8字节)
    /// 用于识别CPI调用日志的前缀
    pub const ANCHOR_SELF_CPI_LOG: [u8; 8] = [228, 69, 165, 46, 81, 203, 154, 29];
}

/// Pump.fun AMM 事件 discriminators
/// 这些值来自 pump_amm_0.1.0.json IDL 文件
pub mod events {
    /// BuyEvent discriminator: [103, 244, 82, 31, 44, 245, 119, 119]
    pub const BUY_EVENT: [u8; 8] = [103, 244, 82, 31, 44, 245, 119, 119];

    /// SellEvent discriminator: [62, 47, 55, 10, 165, 3, 220, 42]
    pub const SELL_EVENT: [u8; 8] = [62, 47, 55, 10, 165, 3, 220, 42];

    /// CreatePoolEvent discriminator: [177, 49, 12, 210, 160, 118, 167, 116]
    pub const CREATE_POOL_EVENT: [u8; 8] = [177, 49, 12, 210, 160, 118, 167, 116];

    /// CreateConfigEvent discriminator: [107, 52, 89, 129, 55, 226, 81, 22]
    pub const CREATE_CONFIG_EVENT: [u8; 8] = [107, 52, 89, 129, 55, 226, 81, 22];

    /// DepositEvent discriminator: [120, 248, 61, 83, 31, 142, 107, 144]
    pub const DEPOSIT_EVENT: [u8; 8] = [120, 248, 61, 83, 31, 142, 107, 144];

    /// WithdrawEvent discriminator: [22, 9, 133, 26, 160, 44, 71, 192]
    pub const WITHDRAW_EVENT: [u8; 8] = [22, 9, 133, 26, 160, 44, 71, 192];

    /// DisableEvent discriminator: [107, 253, 193, 76, 228, 202, 27, 104]
    pub const DISABLE_EVENT: [u8; 8] = [107, 253, 193, 76, 228, 202, 27, 104];

    /// ExtendAccountEvent discriminator: [97, 97, 215, 144, 93, 146, 22, 124]
    pub const EXTEND_ACCOUNT_EVENT: [u8; 8] = [97, 97, 215, 144, 93, 146, 22, 124];

    /// UpdateAdminEvent discriminator: [225, 152, 171, 87, 246, 63, 66, 234]
    pub const UPDATE_ADMIN_EVENT: [u8; 8] = [225, 152, 171, 87, 246, 63, 66, 234];

    /// UpdateFeeConfigEvent discriminator: [90, 23, 65, 35, 62, 244, 188, 208]
    pub const UPDATE_FEE_CONFIG_EVENT: [u8; 8] = [90, 23, 65, 35, 62, 244, 188, 208];

    /// CreateEvent discriminator - 用于代币创建事件 (pump.fun原始程序)
    pub const CREATE_EVENT_EVENT_DISCM: [u8; 8] = [27, 114, 169, 77, 222, 235, 99, 118];

    pub const TRADE_EVENT_EVENT_DISCM: [u8; 8] = [189, 219, 127, 211, 78, 230, 97, 238];
}
