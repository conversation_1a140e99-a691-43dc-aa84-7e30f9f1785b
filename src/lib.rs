// Solana Price Watcher Library
//
// 这个库提供了监控Solana上代币价格的功能，特别是针对Pump.fun生态系统

// === 核心模块 ===
pub mod config;
pub mod constants;
pub mod db;

// === POJO 结构体 ===
pub mod pojo;

// === 核心服务层 ===
pub mod services;

// === 客户端层 ===
pub mod client;

// === 工具层 ===
pub mod parsers;
pub mod utils;

// === 兼容性模块（保持向后兼容） ===
pub mod pool;
pub mod pool_parser;
pub mod handlers;

// === 新功能模块 ===
pub mod token_manager;
pub mod controllers;

// === 主要API导出 ===
pub use config::Config;



// 兼容性导出（保持向后兼容）
pub use pool::{
    TokenPoolFinder,
    AmmPriceMonitor,
};
