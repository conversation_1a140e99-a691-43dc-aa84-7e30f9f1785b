use anyhow::Result;
use log::{info, warn};
use std::collections::HashMap;
use std::time::Instant;

use crate::config::Config;
use crate::client::http_client::HttpRpcClient;
use crate::pool::TokenPoolFinder;
use crate::token_manager::TokenPoolManager;

/// 池子发现控制器
/// 负责批量发现多个token对应的池子
pub struct PoolDiscoveryController {
    /// Token池子管理器
    token_manager: TokenPoolManager,
    /// HTTP RPC客户端
    rpc_client: HttpRpcClient,
    /// Token池子查找器
    token_finder: TokenPoolFinder,
}

impl PoolDiscoveryController {
    /// 创建新的池子发现控制器
    pub async fn new(config: Config, token_list: Vec<String>) -> Result<Self> {
        info!("🚀 初始化池子发现控制器");
        info!("待处理token数量: {}", token_list.len());

        // 创建Token池子管理器
        let token_manager = TokenPoolManager::from_config(&config);

        // 添加额外的token到管理器（如果有的话）
        for token in &token_list {
            token_manager.add_token(token.clone()).await?;
        }

        // 初始化管理器
        token_manager.initialize().await?;

        // 创建HTTP RPC客户端
        let rpc_client = HttpRpcClient::new(config.rpc.clone())?;

        // 创建Token池子查找器
        let token_finder = TokenPoolFinder::new();

        Ok(Self {
            token_manager,
            rpc_client,
            token_finder,
        })
    }
    
    /// 启动池子发现流程（使用gRPC交易订阅）
    pub async fn start_discovery(&mut self) -> Result<PoolDiscoveryResult> {
        info!("🎯 开始池子发现流程（使用gRPC交易订阅）");
        let start_time = Instant::now();

        let mut token_pool_mapping = HashMap::new();
        let mut successful_discoveries = 0;
        let mut failed_discoveries = 0;

        // 获取所有待处理的token
        let pending_tokens = self.token_manager.get_pending_tokens().await;
        let total_tokens = pending_tokens.len();

        info!("待处理token数量: {}", total_tokens);

        if pending_tokens.is_empty() {
            info!("没有待处理的token，跳过发现流程");
            return Ok(PoolDiscoveryResult {
                token_pool_mapping,
                total_tokens: 0,
                successful_discoveries: 0,
                failed_discoveries: 0,
                discovery_duration_seconds: 0,
            });
        }

        // 使用gRPC订阅发现池子
        for (index, token_address) in pending_tokens.iter().enumerate() {
            info!("🔍 处理token {}/{}: {}", index + 1, total_tokens, token_address);

            match self.token_finder.find_pools_by_token(token_address, &self.rpc_client).await {
                Ok(discovery_result) => {
                    let pools = discovery_result.pools;
                    let pool_addresses: Vec<String> = pools
                        .iter()
                        .map(|pool| pool.pool_address.to_string())
                        .collect();

                    if !pools.is_empty() {
                        info!("✅ 为token {} 找到 {} 个池子", token_address, pools.len());
                        for (i, pool_addr) in pool_addresses.iter().enumerate() {
                            info!("  池子 {}: {}", i + 1, pool_addr);
                        }

                        // 标记token为已处理并保存池子关系
                        self.token_manager.mark_token_processed(token_address, pools).await?;
                        successful_discoveries += 1;
                    } else {
                        warn!("⚠️ Token {} 未找到池子", token_address);
                        // 即使没找到池子也标记为已处理
                        self.token_manager.mark_token_processed(token_address, Vec::new()).await?;
                        failed_discoveries += 1;
                    }

                    token_pool_mapping.insert(token_address.clone(), pool_addresses);
                }
                Err(e) => {
                    warn!("❌ 查找token {} 对应的池子失败: {}", token_address, e);
                    // 失败的情况下也标记为已处理，避免重复尝试
                    self.token_manager.mark_token_processed(token_address, Vec::new()).await?;
                    token_pool_mapping.insert(token_address.clone(), Vec::new());
                    failed_discoveries += 1;
                }
            }
        }

        let duration = start_time.elapsed();
        let result = PoolDiscoveryResult {
            token_pool_mapping,
            total_tokens,
            successful_discoveries,
            failed_discoveries,
            discovery_duration_seconds: duration.as_secs(),
        };

        info!("🎉 池子发现流程完成，耗时: {:.2}秒", duration.as_secs_f64());
        Ok(result)
    }

    /// 获取Token池子管理器的引用
    pub fn get_token_manager(&self) -> &TokenPoolManager {
        &self.token_manager
    }

    /// 获取Token池子管理器的可变引用
    pub fn get_token_manager_mut(&mut self) -> &mut TokenPoolManager {
        &mut self.token_manager
    }
}

/// 池子发现结果
#[derive(Debug, Clone)]
pub struct PoolDiscoveryResult {
    /// Token到池子地址的映射
    pub token_pool_mapping: HashMap<String, Vec<String>>,
    /// 总计token数量
    pub total_tokens: usize,
    /// 成功发现池子的token数量
    pub successful_discoveries: usize,
    /// 未发现池子的token数量
    pub failed_discoveries: usize,
    /// 处理耗时（秒）
    pub discovery_duration_seconds: u64,
}

/// 控制器状态
#[derive(Debug, Clone)]
pub struct ControllerStatus {
    /// Token列表
    pub token_list: Vec<String>,
    /// 是否正在运行
    pub is_running: bool,
}

impl PoolDiscoveryResult {
    /// 打印结果摘要
    pub fn print_summary(&self) {
        info!("🎯 池子发现结果摘要:");
        info!("  总计token: {} 个", self.total_tokens);
        info!("  成功发现: {} 个", self.successful_discoveries);
        info!("  发现失败: {} 个", self.failed_discoveries);
        info!("  处理耗时: {} 秒", self.discovery_duration_seconds);

        let success_rate = if self.total_tokens > 0 {
            (self.successful_discoveries as f64 / self.total_tokens as f64) * 100.0
        } else {
            0.0
        };
        info!("  成功率: {:.1}%", success_rate);
    }
}
