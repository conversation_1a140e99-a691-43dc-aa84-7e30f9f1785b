use anyhow::{Result, anyhow};
use log::{debug, error, info};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio_rustls::{TlsConnector, rustls::{ClientConfig, RootCertStore}};

use crate::config::Config;

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub host: String,
    pub port: u16,
    pub enabled: bool,
}

impl Default for ProxyConfig {
    fn default() -> Self {
        Self {
            host: "************".to_string(),
            port: 7897,
            enabled: true,
        }
    }
}

/// 代理客户端
#[derive(Clone)]
pub struct ProxyClient {
    config: ProxyConfig,
}

impl ProxyClient {
    /// 创建新的代理客户端
    pub fn new(config: ProxyConfig) -> Self {
        debug!("创建代理客户端: {}:{}", config.host, config.port);
        Self { config }
    }

    /// 从配置创建代理客户端
    pub fn from_config(_config: &Config) -> Self {
        // 使用默认代理配置
        let proxy_config = ProxyConfig::default();
        Self::new(proxy_config)
    }



    /// 通过代理发送 HTTP 请求
    pub async fn send_http_request(&self, target_url: &str, request_body: &str) -> Result<String> {
        if !self.config.enabled {
            return Err(anyhow!("代理未启用"));
        }

        // 解析目标 URL
        let url = url::Url::parse(target_url).map_err(|e| anyhow!("解析目标 URL 失败: {}", e))?;
        let host = url.host_str().ok_or_else(|| anyhow!("无效的主机名"))?;
        let port = url.port().unwrap_or(if url.scheme() == "https" { 443 } else { 80 });
        let is_https = url.scheme() == "https";

        // 连接到代理服务器
        let mut stream = TcpStream::connect(format!("{}:{}", self.config.host, self.config.port))
            .await
            .map_err(|e| anyhow!("连接代理服务器失败: {}", e))?;

        debug!("已连接到代理服务器");

        // 根据协议类型处理连接
        if is_https {
            // HTTPS需要先发送CONNECT请求建立隧道
            self.establish_tunnel(&mut stream, host, port).await?;

            // 建立TLS连接
            let tls_stream = self.establish_tls_connection(stream, host).await?;

            // 发送HTTP请求并获取响应
            self.send_request_and_get_response(tls_stream, &url, request_body, "HTTPS").await
        } else {
            // HTTP可以直接发送请求（通过代理转发）
            self.send_request_and_get_response(stream, &url, request_body, "HTTP").await
        }
    }

    /// 建立CONNECT隧道（仅用于HTTPS）
    async fn establish_tunnel(&self, stream: &mut TcpStream, host: &str, port: u16) -> Result<()> {
        let connect_request = format!(
            "CONNECT {}:{} HTTP/1.1\r\nHost: {}:{}\r\n\r\n",
            host, port, host, port
        );

        debug!("发送 CONNECT 请求: {}", connect_request.trim());

        stream
            .write_all(connect_request.as_bytes())
            .await
            .map_err(|e| anyhow!("发送 CONNECT 请求失败: {}", e))?;

        // 读取 CONNECT 响应
        let mut buffer = vec![0; 1024];
        let n = stream
            .read(&mut buffer)
            .await
            .map_err(|e| anyhow!("读取 CONNECT 响应失败: {}", e))?;

        let response = String::from_utf8_lossy(&buffer[..n]);
        debug!("CONNECT 响应: {}", response.trim());

        if !response.contains("200") {
            return Err(anyhow!("CONNECT 请求失败: {}", response.trim()));
        }

        debug!("CONNECT 隧道建立成功");
        Ok(())
    }

    /// 建立TLS连接
    async fn establish_tls_connection(&self, stream: TcpStream, host: &str) -> Result<tokio_rustls::client::TlsStream<TcpStream>> {
        let mut root_store = RootCertStore::empty();
        root_store.add_trust_anchors(
            webpki_roots::TLS_SERVER_ROOTS.iter().map(|ta| {
                rustls::OwnedTrustAnchor::from_subject_spki_name_constraints(
                    ta.subject.to_vec(),
                    ta.subject_public_key_info.to_vec(),
                    ta.name_constraints.as_ref().map(|nc| nc.to_vec()),
                )
            })
        );

        let config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(root_store)
            .with_no_client_auth();

        let connector = TlsConnector::from(Arc::new(config));
        let domain = rustls::ServerName::try_from(host)
            .map_err(|e| anyhow!("无效的域名: {}", e))?;

        // 将 TCP 流升级为 TLS 流
        let tls_stream = connector.connect(domain, stream).await
            .map_err(|e| anyhow!("TLS 连接失败: {}", e))?;

        debug!("TLS 连接建立成功");
        Ok(tls_stream)
    }

    /// 发送HTTP请求并获取响应（通用方法）
    async fn send_request_and_get_response<S>(&self, mut stream: S, url: &url::Url, request_body: &str, protocol: &str) -> Result<String>
    where
        S: AsyncReadExt + AsyncWriteExt + Unpin,
    {
        // 构建 HTTP 请求
        let host = url.host_str().ok_or_else(|| anyhow!("无效的主机名"))?;
        let path = if url.path().is_empty() { "/" } else { url.path() };
        let query = url.query().map(|q| format!("?{}", q)).unwrap_or_default();

        let http_request = format!(
            "POST {}{} HTTP/1.1\r\n\
             Host: {}\r\n\
             Content-Type: application/json\r\n\
             Content-Length: {}\r\n\
             Connection: close\r\n\
             \r\n\
             {}",
            path,
            query,
            host,
            request_body.len(),
            request_body
        );

        debug!("发送 {} 请求: {}", protocol, http_request.lines().next().unwrap_or(""));

        // 发送请求
        stream
            .write_all(http_request.as_bytes())
            .await
            .map_err(|e| anyhow!("发送 {} 请求失败: {}", protocol, e))?;

        // 读取响应
        let mut response_buffer = Vec::new();
        stream
            .read_to_end(&mut response_buffer)
            .await
            .map_err(|e| anyhow!("读取响应失败: {}", e))?;

        let response = String::from_utf8_lossy(&response_buffer);
        debug!("收到 {} 响应长度: {} bytes", protocol, response.len());

        // 解析 HTTP 响应
        self.parse_http_response(&response, protocol)
    }

    /// 解析HTTP响应（通用方法）
    fn parse_http_response(&self, response: &str, protocol: &str) -> Result<String> {
        if let Some(body_start) = response.find("\r\n\r\n") {
            let body = &response[body_start + 4..];
            debug!("响应体内容: {}", body);

            if response.contains("200 OK") {
                info!("{} 请求成功", protocol);
                Ok(body.to_string())
            } else {
                error!(
                    "{} 请求失败: {}",
                    protocol,
                    &response[..response.find('\r').unwrap_or(100).min(response.len())]
                );
                error!("完整响应: {}", response);
                Err(anyhow!("{} 请求失败", protocol))
            }
        } else {
            error!("无效的 HTTP 响应格式");
            error!("完整响应: {}", response);
            Err(anyhow!("无效的 HTTP 响应格式"))
        }
    }

    /// 获取代理配置
    pub fn get_config(&self) -> &ProxyConfig {
        &self.config
    }
}
