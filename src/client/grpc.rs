use anyhow::{anyhow, Result};
use backoff::{future::retry, ExponentialBackoff};
use log::{debug, info, warn, error};
use serde::{Deserialize, Serialize};
use solana_sdk::{account::Account, pubkey::Pubkey};
use std::{collections::HashMap, str::FromStr, time::Duration};
use tokio::time;
use futures::{sink::SinkExt, stream::StreamExt};
use tonic::transport::ClientTlsConfig;
use yellowstone_grpc_client::{GeyserGrpcClient, Interceptor};
use yellowstone_grpc_proto::{
    geyser::SubscribeRequestFilterTransactions,
    prelude::{
        subscribe_update::UpdateOneof, CommitmentLevel, SubscribeRequest, SubscribeRequestPing,
    },
};
use crate::pojo::solana::grpc::serializable::SerializableSubscribeRequest;
use pumpfun_amm_interface::instructions::PumpfunAmmProgramIx;

use crate::{config::GrpcConfig, constants};
use super::proxy::ProxyClient;

// 常量定义（保留用于将来可能的使用）

type TxnFilterMap = HashMap<String, SubscribeRequestFilterTransactions>;

/// gRPC 账户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcAccountInfo {
    pub pubkey: Pubkey,
    pub account: Account,
    pub slot: u64,
}

/// gRPC 连接参数
#[derive(Debug, Clone)]
pub struct GrpcConnectionArgs {
    pub endpoint: String,
    pub x_token: Option<String>,
    pub timeout_seconds: u64,

}

impl From<&GrpcConfig> for GrpcConnectionArgs {
    fn from(config: &GrpcConfig) -> Self {
        Self {
            endpoint: config.endpoint.clone(),
            x_token: config.x_token.clone(),
            timeout_seconds: config.timeout_seconds,
        }
    }
}

impl GrpcConnectionArgs {
    /// 连接到 gRPC 服务器
    pub async fn connect(&self) -> Result<GeyserGrpcClient<impl Interceptor>> {
        use std::env;

        info!("连接到 gRPC 服务器: {}", self.endpoint);

        // 设置代理环境变量
        env::set_var("HTTP_PROXY", "http://192.168.96.1:7897");
        env::set_var("HTTPS_PROXY", "http://192.168.96.1:7897");
        info!("设置代理: http://192.168.96.1:7897");

        let mut builder = GeyserGrpcClient::build_from_shared(self.endpoint.clone())?;

        // 只有在提供了 token 时才设置认证令牌
        if let Some(token) = &self.x_token {
            if !token.is_empty() {
                builder = builder.x_token(Some(token.clone()))?;
                info!("使用认证令牌");
            }
        }

        let client = builder
            .connect_timeout(Duration::from_secs(self.timeout_seconds))
            .timeout(Duration::from_secs(self.timeout_seconds))
            .tls_config(ClientTlsConfig::new().with_native_roots())?
            .max_decoding_message_size(1024 * 1024 * 1024)
            .connect()
            .await?;

        info!("成功连接到 gRPC 服务器");
        Ok(client)
    }

    /// 创建交易订阅请求
    pub fn create_transaction_subscription(&self, program_ids: Vec<String>) -> SubscribeRequest {
        let mut transactions: TxnFilterMap = HashMap::new();

        transactions.insert(
            "client".to_owned(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: program_ids.clone(),
                account_exclude: vec![],
                account_required: vec![constants::programs::PUMP_AMM_PROGRAM_ID.to_string()],
                signature: None,
            },
        );

        let request = SubscribeRequest {
            accounts: HashMap::default(),
            slots: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: Vec::default(),
            ping: None,
            from_slot: None,
        };

        // 打印发送给gRPC的消息
        info!("=== 发送给gRPC的订阅请求 ===");
        info!("端点: {}", self.endpoint);
        info!("监控程序ID: {:?}", program_ids);
        info!("必需账户: [\"5ApSDnWK155kcqnrs5ZYTrVMN7wVeoXNQnrh696Vpump\"]");

        // #debug-start#
        // 转换为可序列化结构体并输出JSON
        #[cfg(debug_assertions)]
        {
            let serializable_request = SerializableSubscribeRequest::from_grpc_request(&request);
            match serde_json::to_string_pretty(&serializable_request) {
                Ok(json_str) => info!("请求内容: {}", json_str),
                Err(_) => info!("请求内容: {:#?}", request),
            }
        }
        // #debug-end#
        info!("================================");

        request
    }

    /// 创建交易订阅请求（针对特定池子）
    pub fn create_transaction_subscription_for_pool(&self, program_ids: Vec<String>, pool_address: String) -> SubscribeRequest {
        let mut transactions: TxnFilterMap = HashMap::new();

        transactions.insert(
            "client".to_owned(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: program_ids.clone(),
                account_exclude: vec![],
                account_required: vec![pool_address.clone()],
                signature: None,
            },
        );

        let request = SubscribeRequest {
            accounts: HashMap::default(),
            slots: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: Vec::default(),
            ping: None,
            from_slot: None,
        };

        // 打印发送给gRPC的消息
        info!("=== 发送给gRPC的池子订阅请求 ===");
        info!("端点: {}", self.endpoint);
        info!("监控程序ID: {:?}", program_ids);
        info!("必需账户（池子地址）: [{}]", pool_address);

        // #debug-start#
        // 转换为可序列化结构体并输出JSON
        #[cfg(debug_assertions)]
        {
            let serializable_request = SerializableSubscribeRequest::from_grpc_request(&request);
            match serde_json::to_string_pretty(&serializable_request) {
                Ok(json_str) => info!("请求内容: {}", json_str),
                Err(_) => info!("请求内容: {:#?}", request),
            }
        }
        // #debug-end#
        info!("====================================");

        request
    }

    /// 创建交易订阅请求（针对多个池子）
    pub fn create_transaction_subscription_for_multiple_pools(&self, program_ids: Vec<String>, pool_addresses: Vec<String>) -> SubscribeRequest {
        let mut transactions: TxnFilterMap = HashMap::new();

        transactions.insert(
            "client".to_owned(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: program_ids.clone(),
                account_exclude: vec![],
                account_required: pool_addresses.clone(),
                signature: None,
            },
        );

        let request = SubscribeRequest {
            accounts: HashMap::default(),
            slots: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: Vec::default(),
            ping: None,
            from_slot: None,
        };

        // 打印发送给gRPC的消息
        info!("=== 发送给gRPC的多池子订阅请求 ===");
        info!("端点: {}", self.endpoint);
        info!("监控程序ID: {:?}", program_ids);
        info!("必需账户（池子地址）: {:?}", pool_addresses);

        // #debug-start#
        // 转换为可序列化结构体并输出JSON
        #[cfg(debug_assertions)]
        {
            let serializable_request = SerializableSubscribeRequest::from_grpc_request(&request);
            match serde_json::to_string_pretty(&serializable_request) {
                Ok(json_str) => info!("请求内容: {}", json_str),
                Err(_) => info!("请求内容: {:#?}", request),
            }
        }
        // #debug-end#
        info!("====================================");

        request
    }

    /// 创建账户订阅请求
    pub fn create_account_subscription(&self, account_addresses: Vec<String>) -> SubscribeRequest {
        use yellowstone_grpc_proto::geyser::SubscribeRequestFilterAccounts;

        let mut accounts = HashMap::new();

        accounts.insert(
            "client".to_owned(),
            SubscribeRequestFilterAccounts {
                account: account_addresses.clone(),
                owner: vec![],
                filters: vec![],
                nonempty_txn_signature: None,
            },
        );

        let request = SubscribeRequest {
            accounts,
            slots: HashMap::default(),
            transactions: HashMap::default(),
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: Vec::default(),
            ping: None,
            from_slot: None,
        };

        // 打印发送给gRPC的消息
        info!("=== 发送给gRPC的账户订阅请求 ===");
        info!("端点: {}", self.endpoint);
        info!("监控账户: {:?}", account_addresses);

        // #debug-start#
        // 转换为可序列化结构体并输出JSON
        #[cfg(debug_assertions)]
        {
            let serializable_request = SerializableSubscribeRequest::from_grpc_request(&request);
            match serde_json::to_string_pretty(&serializable_request) {
                Ok(json_str) => info!("请求内容: {}", json_str),
                Err(_) => info!("请求内容: {:#?}", request),
            }
        }
        // #debug-end#
        info!("====================================");

        request
    }
}

/// Yellowstone gRPC 客户端
pub struct YellowstoneGrpcClient {
    config: GrpcConfig,
    proxy_client: Option<ProxyClient>,
}

impl YellowstoneGrpcClient {
    /// 创建新的 gRPC 客户端
    pub fn new(config: GrpcConfig) -> Result<Self> {
        debug!("创建 Yellowstone gRPC 客户端");
        debug!("端点: {}", config.endpoint);
        debug!("超时: {}秒", config.timeout_seconds);
        debug!("使用 TLS: {}", config.use_tls);

        // 验证端点格式
        if !config.endpoint.starts_with("http") {
            return Err(anyhow!("无效的 gRPC 端点格式: {}", config.endpoint));
        }

        info!("Yellowstone gRPC 客户端创建成功");

        Ok(Self {
            config,
            proxy_client: None,
        })
    }

    /// 获取连接参数
    pub fn get_connection_args(&self) -> GrpcConnectionArgs {
        GrpcConnectionArgs::from(&self.config)
    }

    /// 启动交易流监听
    pub async fn start_transaction_stream(&self, program_ids: Vec<String>) -> Result<()> {
        let args = self.get_connection_args();

        info!("启动交易流监听");
        info!("监控程序ID: {:?}", program_ids);

        // 连接重试逻辑
        retry(ExponentialBackoff::default(), move || {
            let args = args.clone();
            let program_ids = program_ids.clone();

            async move {
                info!("尝试连接到 gRPC 服务器");

                let client = args.connect().await.map_err(backoff::Error::transient)?;

                let request = args.create_transaction_subscription(program_ids.clone());
                info!("创建订阅请求，监控程序: {:?}", program_ids);

                Self::process_transaction_stream(client, request)
                    .await
                    .map_err(backoff::Error::transient)?;

                Ok::<(), backoff::Error<anyhow::Error>>(())
            }
        })
        .await
        .map_err(Into::into)
    }

    /// 启动交易流监听（针对特定池子）
    pub async fn start_transaction_stream_for_pool(&self, program_ids: Vec<String>, pool_address: String) -> Result<()> {
        let args = self.get_connection_args();

        info!("启动交易流监听（针对特定池子）");
        info!("监控程序ID: {:?}", program_ids);
        info!("特定池子: {}", pool_address);

        // 连接重试逻辑
        retry(ExponentialBackoff::default(), move || {
            let args = args.clone();
            let program_ids = program_ids.clone();
            let pool_address = pool_address.clone();

            async move {
                info!("尝试连接到 gRPC 服务器");

                let client = args.connect().await.map_err(backoff::Error::transient)?;

                let request = args.create_transaction_subscription_for_pool(program_ids.clone(), pool_address.clone());
                info!("创建订阅请求，监控程序: {:?}，池子: {}", program_ids, pool_address);

                Self::process_transaction_stream(client, request)
                    .await
                    .map_err(backoff::Error::transient)?;

                Ok::<(), backoff::Error<anyhow::Error>>(())
            }
        })
        .await
        .map_err(Into::into)
    }

    /// 启动交易流监听（针对多个池子）
    pub async fn start_transaction_stream_for_multiple_pools(&self, program_ids: Vec<String>, pool_addresses: Vec<String>) -> Result<()> {
        let args = self.get_connection_args();

        info!("启动交易流监听（针对多个池子）");
        info!("监控程序ID: {:?}", program_ids);
        info!("监控池子数量: {}", pool_addresses.len());
        info!("池子地址: {:?}", pool_addresses);

        // 连接重试逻辑
        retry(ExponentialBackoff::default(), move || {
            let args = args.clone();
            let program_ids = program_ids.clone();
            let pool_addresses = pool_addresses.clone();

            async move {
                info!("尝试连接到 gRPC 服务器");

                let client = args.connect().await.map_err(backoff::Error::transient)?;

                let request = args.create_transaction_subscription_for_multiple_pools(program_ids.clone(), pool_addresses.clone());
                info!("创建订阅请求，监控程序: {:?}，池子数量: {}", program_ids, pool_addresses.len());

                Self::process_transaction_stream(client, request)
                    .await
                    .map_err(backoff::Error::transient)?;

                Ok::<(), backoff::Error<anyhow::Error>>(())
            }
        })
        .await
        .map_err(Into::into)
    }

    /// 启动账户流监听
    pub async fn start_account_stream(&self, account_addresses: Vec<String>) -> Result<()> {
        let args = self.get_connection_args();

        info!("启动账户流监听");
        info!("监控账户: {:?}", account_addresses);

        // 连接重试逻辑
        retry(ExponentialBackoff::default(), move || {
            let args = args.clone();
            let account_addresses = account_addresses.clone();

            async move {
                info!("尝试连接到 gRPC 服务器");

                let client = args.connect().await.map_err(backoff::Error::transient)?;

                let request = args.create_account_subscription(account_addresses.clone());
                info!("创建账户订阅请求，监控账户: {:?}", account_addresses);

                Self::process_account_stream(client, request)
                    .await
                    .map_err(backoff::Error::transient)?;

                Ok::<(), backoff::Error<anyhow::Error>>(())
            }
        })
        .await
        .map_err(Into::into)
    }

    /// 处理交易流
    async fn process_transaction_stream(
        mut client: GeyserGrpcClient<impl Interceptor>,
        request: SubscribeRequest,
    ) -> Result<()> {
        info!("交易流已开启");

        let (mut subscribe_tx, mut stream) = client.subscribe_with_request(Some(request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    // 只对交易消息打印debug信息，忽略ping/pong
                    match &msg.update_oneof {
                        Some(UpdateOneof::Transaction(_)) => {
                            debug!("=== 接收到gRPC交易消息 ===");
                        }
                        _ => {} // 不打印ping/pong等其他消息
                    }

                    match msg.update_oneof {
                        Some(UpdateOneof::Transaction(update)) => {
                            info!("收到交易更新，slot: {}", update.slot);
                            if let Some(txn) = update.transaction {
                                Self::process_transaction_update(txn).await?;
                            }
                        }
                        Some(UpdateOneof::Ping(_)) => {
                            // 发送Pong响应（不打印日志）
                            subscribe_tx
                                .send(SubscribeRequest {
                                    ping: Some(SubscribeRequestPing { id: 1 }),
                                    ..Default::default()
                                })
                                .await?;
                        }
                        Some(UpdateOneof::Pong(_)) => {
                            // 收到Pong响应（不打印日志）
                        }
                        None => {
                            error!("update not found in the message");
                            break;
                        }
                        _ => {
                            debug!("收到其他类型的更新");
                        }
                    }
                },
                Err(error) => {
                    error!("gRPC stream error: {error:?}");
                    break;
                }
            }
        }

        info!("交易流已关闭");
        Ok(())
    }

    /// 处理账户流
    async fn process_account_stream(
        mut client: GeyserGrpcClient<impl Interceptor>,
        request: SubscribeRequest,
    ) -> Result<()> {
        info!("账户流已开启");

        let (mut subscribe_tx, mut stream) = client.subscribe_with_request(Some(request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    // 打印接收到的消息
                    debug!("=== 接收到gRPC账户消息 ===");
                    debug!("消息类型: {:?}", msg.update_oneof);
                    debug!("========================");

                    match msg.update_oneof {
                        Some(UpdateOneof::Account(update)) => {
                            info!("收到账户更新，slot: {}", update.slot);
                            Self::process_account_update(update).await?;
                        }
                        Some(UpdateOneof::Ping(_)) => {
                            // 发送Pong响应（不打印日志）
                            subscribe_tx
                                .send(SubscribeRequest {
                                    ping: Some(SubscribeRequestPing { id: 1 }),
                                    ..Default::default()
                                })
                                .await?;
                        }
                        Some(UpdateOneof::Pong(_)) => {
                            // 收到Pong响应（不打印日志）
                        }
                        None => {
                            error!("update not found in the message");
                            break;
                        }
                        _ => {
                            debug!("收到其他类型的更新");
                        }
                    }
                },
                Err(error) => {
                    error!("gRPC stream error: {error:?}");
                    break;
                }
            }
        }

        info!("账户流已关闭");
        Ok(())
    }

    /// 处理交易更新
    async fn process_transaction_update(
        txn: yellowstone_grpc_proto::prelude::SubscribeUpdateTransactionInfo,
    ) -> Result<()> {
        let signature = bs58::encode(&txn.signature).into_string();
        debug!("处理交易: {}", signature);

        // 检查是否有元数据
        if let Some(meta) = &txn.meta {
            // 检查交易是否成功
            if meta.err.is_some() {
                debug!("跳过失败的交易: {}", signature);
                return Ok(());
            }

            // 处理日志消息中的pump.fun事件
            if !meta.log_messages.is_empty() {
                Self::process_pump_events_from_logs(&meta.log_messages, &signature).await?;
            }

            // 处理指令中的CPI事件
            if let Some(transaction) = &txn.transaction {
                if let Some(message) = &transaction.message {
                    Self::process_pump_events_from_instructions(&message.instructions, &signature).await?;
                }
            }
        }

        Ok(())
    }

    /// 从日志消息中处理pump.fun事件
    async fn process_pump_events_from_logs(
        log_messages: &[String],
        signature: &str,
    ) -> Result<()> {
        for log_message in log_messages {
            // 查找Program data日志（包含CPI事件数据）
            if log_message.starts_with("Program data: ") {
                let data_part = &log_message[14..]; // 移除 "Program data: " 前缀

                // 尝试解析为pump.fun事件
                if let Ok(event_data) = bs58::decode(data_part).into_vec() {
                    Self::parse_and_display_pump_event(&event_data, signature).await?;
                }
            }
        }
        Ok(())
    }

    /// 从指令中处理pump.fun事件
    async fn process_pump_events_from_instructions(
        instructions: &[yellowstone_grpc_proto::prelude::CompiledInstruction],
        signature: &str,
    ) -> Result<()> {
        for instruction in instructions {
            // 检查指令数据是否包含pump.fun事件
            if !instruction.data.is_empty() {
                Self::parse_and_display_pump_event(&instruction.data, signature).await?;
            }
        }
        Ok(())
    }

    /// 解析并显示pump.fun事件
    async fn parse_and_display_pump_event(
        event_data: &[u8],
        signature: &str,
    ) -> Result<()> {
        use crate::constants::events::*;

        // 检查数据长度
        if event_data.len() < 8 {
            return Ok(());
        }

        // 提取事件判别器
        let discriminator = &event_data[0..8];

        // 根据判别器类型处理不同事件
        if discriminator == BUY_EVENT {
            if let Ok(buy_event) = crate::services::cpi_pool_extractor::parse_buy_event_data(&bs58::encode(event_data).into_string()) {
                let sol_amount = buy_event.quote_amount_out as f64 / 1_000_000_000.0;
                let token_amount = buy_event.base_amount_in as f64 / 1_000_000.0;
                let price = if token_amount > 0.0 { sol_amount / token_amount } else { 0.0 };

                info!("🟢 买入交易 | 签名: {} | 代币: {:.2} | SOL: {:.6} | 价格: {:.9} SOL | 池子: {}",
                    signature,
                    token_amount,
                    sol_amount,
                    price,
                    buy_event.pool
                );
            }
        } else if discriminator == SELL_EVENT {
            if let Ok(sell_event) = crate::services::cpi_pool_extractor::parse_sell_event_data(&bs58::encode(event_data).into_string()) {
                let sol_amount = sell_event.quote_amount_out as f64 / 1_000_000_000.0;
                let token_amount = sell_event.base_amount_in as f64 / 1_000_000.0;
                let price = if token_amount > 0.0 { sol_amount / token_amount } else { 0.0 };

                info!("🔴 卖出交易 | 签名: {} | 代币: {:.2} | SOL: {:.6} | 价格: {:.9} SOL | 池子: {}",
                    signature,
                    token_amount,
                    sol_amount,
                    price,
                    sell_event.pool
                );
            }
        }

        Ok(())
    }

    /// 处理账户更新
    async fn process_account_update(
        update: yellowstone_grpc_proto::prelude::SubscribeUpdateAccount,
    ) -> Result<()> {
        info!("=== 账户更新 ===");
        info!("Slot: {}", update.slot);
        info!("是否启动: {}", update.is_startup);

        if let Some(account) = &update.account {
            let account_pubkey = bs58::encode(&account.pubkey).into_string();
            info!("账户地址: {}", account_pubkey);
            info!("账户所有者: {}", hex::encode(&account.owner));
            info!("账户余额: {} lamports", account.lamports);
            info!("数据长度: {} bytes", account.data.len());
            info!("可执行: {}", account.executable);
            info!("租金周期: {}", account.rent_epoch);

            // 根据账户所有者判断账户类型
            let owner_str = hex::encode(&account.owner);
            debug!("检查账户类型，所有者: {}", owner_str);

            // 检查是否为PumpFun AMM程序账户
            if owner_str == "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA" {
                debug!("检测到PumpFun AMM程序账户");

                info!("🏊 检测到PumpFun AMM程序账户: {}", account_pubkey);
            } else {
                debug!("非PumpFun AMM程序账户，跳过Pool解析");
                debug!("账户所有者: {}", owner_str);
            }
        }
        info!("===============");

        Ok(())
    }

    /// 处理交易指令
    async fn process_transaction_instructions(
        instructions: &[yellowstone_grpc_proto::prelude::CompiledInstruction],
        signature: &str,
    ) -> Result<()> {
        for (idx, instruction) in instructions.iter().enumerate() {
            debug!("处理指令 {} 在交易 {}", idx, signature);

            // 尝试解码 PumpFun 指令
            match PumpfunAmmProgramIx::deserialize(&instruction.data) {
                Ok(decoded_ix) => {
                    info!("PumpFun 指令: {} - {:?}", decoded_ix.name(), decoded_ix);
                }
                Err(_) => {
                    debug!("非 PumpFun 指令或解码失败");
                }
            }
        }

        Ok(())
    }

    /// 创建带代理的 gRPC 客户端
    pub fn new_with_proxy(config: GrpcConfig, proxy_client: ProxyClient) -> Result<Self> {
        debug!("创建带代理的 Yellowstone gRPC 客户端");
        debug!("端点: {}", config.endpoint);
        debug!(
            "代理: {}:{}",
            proxy_client.get_config().host,
            proxy_client.get_config().port
        );

        // 验证端点格式
        if !config.endpoint.starts_with("http") {
            return Err(anyhow!("无效的 gRPC 端点格式: {}", config.endpoint));
        }

        info!("带代理的 Yellowstone gRPC 客户端创建成功");

        Ok(Self {
            config,
            proxy_client: Some(proxy_client),
        })
    }

    /// 连接到 gRPC 服务器
    pub async fn connect(&self) -> Result<()> {
        debug!("连接到 Yellowstone gRPC 服务器: {}", self.config.endpoint);

        // 这里将来会实现实际的 gRPC 连接逻辑
        // 目前使用模拟连接

        info!("成功连接到 Yellowstone gRPC 服务器");
        Ok(())
    }

    /// 获取账户信息
    pub async fn get_account_info(&self, pubkey: &Pubkey) -> Result<GrpcAccountInfo> {
        debug!("通过 gRPC 获取账户信息: {}", pubkey);

        // 记录请求详情
        debug!("=== gRPC 请求详情 ===");
        debug!("端点: {}", self.config.endpoint);
        debug!("账户地址: {}", pubkey);
        debug!("超时时间: {}秒", self.config.timeout_seconds);
        debug!("使用 TLS: {}", self.config.use_tls);
        if let Some(proxy) = &self.proxy_client {
            debug!(
                "使用代理: {}:{}",
                proxy.get_config().host,
                proxy.get_config().port
            );
        }
        debug!("==================");

        // 如果有代理，通过代理发送请求
        if let Some(proxy) = &self.proxy_client {
            debug!("通过代理发送 gRPC 请求");

            // 构建 gRPC 请求数据
            let request_data = format!(
                r#"{{"jsonrpc":"2.0","id":1,"method":"getAccountInfo","params":["{}"]}}"#,
                pubkey
            );

            match proxy
                .send_http_request(&self.config.endpoint, &request_data)
                .await
            {
                Ok(response) => {
                    debug!("代理请求成功，响应长度: {} bytes", response.len());
                }
                Err(e) => {
                    warn!("代理请求失败: {}，使用模拟数据", e);
                }
            }
        }

        // 模拟网络延迟
        time::sleep(Duration::from_millis(100)).await;

        // 创建模拟账户数据
        let mock_account = Account {
            lamports: **********,                                                    // 1 SOL
            data: vec![0; 128],                                                      // 模拟账户数据
            owner: Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?, // Pump.fun 程序 ID
            executable: false,
            rent_epoch: 0,
        };

        let account_info = GrpcAccountInfo {
            pubkey: *pubkey,
            account: mock_account,
            slot: *********, // 模拟 slot
        };

        // 记录响应详情
        debug!("=== gRPC 响应详情 ===");
        debug!("账户地址: {}", account_info.pubkey);
        debug!("账户所有者: {}", account_info.account.owner);
        debug!("账户余额: {} lamports", account_info.account.lamports);
        debug!("数据长度: {} bytes", account_info.account.data.len());
        debug!("Slot: {}", account_info.slot);
        debug!("==================");

        info!("成功获取账户信息: {}", pubkey);
        Ok(account_info)
    }

    /// 批量获取账户信息
    pub async fn get_multiple_accounts(
        &self,
        pubkeys: &[Pubkey],
    ) -> Result<Vec<Option<GrpcAccountInfo>>> {
        debug!("批量获取 {} 个账户信息", pubkeys.len());

        let mut results = Vec::new();

        for pubkey in pubkeys {
            match self.get_account_info(pubkey).await {
                Ok(account_info) => {
                    results.push(Some(account_info));
                }
                Err(e) => {
                    warn!("获取账户信息失败 {}: {}", pubkey, e);
                    results.push(None);
                }
            }
        }

        info!(
            "批量获取账户信息完成，成功: {}/{}",
            results.iter().filter(|r| r.is_some()).count(),
            pubkeys.len()
        );

        Ok(results)
    }

    /// 订阅账户变化
    pub async fn subscribe_account_updates(&self, pubkey: &Pubkey) -> Result<()> {
        debug!("订阅账户变化: {}", pubkey);

        // 这里将来会实现实际的订阅逻辑
        // 目前只是记录日志

        info!("已订阅账户变化: {}", pubkey);
        Ok(())
    }

    /// 取消订阅账户变化
    pub async fn unsubscribe_account_updates(&self, pubkey: &Pubkey) -> Result<()> {
        debug!("取消订阅账户变化: {}", pubkey);

        // 这里将来会实现实际的取消订阅逻辑

        info!("已取消订阅账户变化: {}", pubkey);
        Ok(())
    }

    /// 获取当前 slot
    pub async fn get_slot(&self) -> Result<u64> {
        debug!("获取当前 slot");

        // 模拟获取 slot
        let slot = *********;

        debug!("当前 slot: {}", slot);
        Ok(slot)
    }

    /// 检查连接状态
    pub async fn check_health(&self) -> Result<bool> {
        debug!("检查 gRPC 连接健康状态");

        // 这里将来会实现实际的健康检查
        // 目前总是返回健康状态

        info!("gRPC 连接健康");
        Ok(true)
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &GrpcConfig {
        &self.config
    }

    /// 设置超时时间
    pub fn set_timeout(&mut self, timeout_seconds: u64) {
        debug!("设置 gRPC 超时时间: {}秒", timeout_seconds);
        self.config.timeout_seconds = timeout_seconds;
    }
}



