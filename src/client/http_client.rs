use anyhow::{Result, anyhow};
use log::debug;
use serde::Deserialize;
use serde_json::Value;
use std::collections::HashMap;
use reqwest::Client;

use crate::config::{RpcConfig, Config};
use crate::pojo::solana::http::req::{HttpRequestConfig, RpcRequest};

/// HTTP RPC 客户端
pub struct HttpRpcClient {
    config: RpcConfig,
    client: Client,
    request_id: std::sync::atomic::AtomicU64,
}

impl Clone for HttpRpcClient {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            client: self.client.clone(),
            request_id: std::sync::atomic::AtomicU64::new(0),
        }
    }
}

impl HttpRpcClient {
    /// 创建新的 HTTP RPC 客户端
    pub fn new(config: RpcConfig) -> Result<Self> {
        debug!("创建 HTTP RPC 客户端: {}", config.endpoint);

        // 创建reqwest客户端，支持代理配置
        let mut client_builder = Client::builder();

        // 从全局配置获取代理设置
        if let Ok(global_config) = Config::load_from_file("config.toml") {
            let proxy_cfg = &global_config.proxy;
            if proxy_cfg.enabled {
                let proxy_url = format!("http://{}:{}", proxy_cfg.host, proxy_cfg.port);
                debug!("配置HTTP代理: {}", proxy_url);

                let mut proxy = reqwest::Proxy::all(&proxy_url)?;

                // 如果有用户名和密码，添加认证
                if let (Some(username), Some(password)) = (&proxy_cfg.username, &proxy_cfg.password) {
                    proxy = proxy.basic_auth(username, password);
                    debug!("代理认证已配置");
                }

                client_builder = client_builder.proxy(proxy);
            }
        }

        let client = client_builder.build()?;

        Ok(Self {
            config,
            client,
            request_id: std::sync::atomic::AtomicU64::new(1),
        })
    }

    /// 通用HTTP请求方法，支持泛型返回类型
    pub async fn send_request<T>(&self, request_config: HttpRequestConfig) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        debug!("发送HTTP请求: {} {}", request_config.method, request_config.url);
        debug!("请求头: {:?}", request_config.headers);
        if let Some(ref body) = request_config.body {
            debug!("请求体: {}", body);
        }

        // 构建reqwest请求
        let method = match request_config.method.to_uppercase().as_str() {
            "GET" => reqwest::Method::GET,
            "POST" => reqwest::Method::POST,
            "PUT" => reqwest::Method::PUT,
            "DELETE" => reqwest::Method::DELETE,
            "PATCH" => reqwest::Method::PATCH,
            _ => return Err(anyhow!("不支持的HTTP方法: {}", request_config.method)),
        };

        let mut request_builder = self.client
            .request(method, &request_config.url);

        // 添加请求头
        for (key, value) in request_config.headers {
            request_builder = request_builder.header(&key, &value);
        }

        // 添加请求体
        if let Some(body) = request_config.body {
            request_builder = request_builder.body(body);
        }

        // 发送请求
        let response = request_builder.send().await
            .map_err(|e| anyhow!("HTTP请求失败: {}", e))?;

        debug!("响应状态: {}", response.status());

        // 获取响应文本
        let response_body = response.text().await
            .map_err(|e| anyhow!("读取响应内容失败: {}", e))?;

        debug!("响应内容: {}", response_body);

        // 解析为指定的泛型类型
        let parsed_response: T = serde_json::from_str(&response_body)
            .map_err(|e| anyhow!("解析响应失败: {}", e))?;

        Ok(parsed_response)
    }

    /// 发送RPC请求的便捷方法
    pub async fn send_rpc_request<T>(&self, method: &str, params: Value) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let request_id = self.request_id.fetch_add(1, std::sync::atomic::Ordering::SeqCst);

        let rpc_request = RpcRequest::new(request_id, method, params);
        let request_body = serde_json::to_string(&rpc_request)?;

        let request_config = HttpRequestConfig {
            url: self.config.endpoint.clone(),
            method: "POST".to_string(),
            headers: {
                let mut headers = HashMap::new();
                headers.insert("Content-Type".to_string(), "application/json".to_string());
                headers
            },
            body: Some(request_body),
        };

        self.send_request(request_config).await
    }

    /// 通用RPC调用方法，可以调用任何Solana RPC方法
    pub async fn call_rpc<T>(&self, method: &str, params: Value) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        debug!("调用RPC方法: {}", method);
        self.send_rpc_request(method, params).await
    }

    /// 使用自定义URL和参数发送请求
    pub async fn send_custom_request<T>(&self, url: &str, method: &str, body: Option<&str>, headers: Option<HashMap<String, String>>) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let request_config = HttpRequestConfig {
            url: url.to_string(),
            method: method.to_string(),
            headers: headers.unwrap_or_else(|| {
                let mut default_headers = HashMap::new();
                default_headers.insert("Content-Type".to_string(), "application/json".to_string());
                default_headers
            }),
            body: body.map(|s| s.to_string()),
        };

        self.send_request(request_config).await
    }
}
