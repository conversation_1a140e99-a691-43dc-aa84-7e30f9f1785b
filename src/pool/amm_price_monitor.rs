use anyhow::{Result, anyhow};


use log::{debug, info, warn};
use solana_sdk::pubkey::Pubkey;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use crate::client::http_client::HttpRpcClient;

use super::token_pool_finder::PoolSearchResult;

/// AMM价格监控输出
#[derive(Debug, Clone)]
pub struct AmmPriceOutput {
    /// 池子地址
    pub pool_address: String,
    /// 基础代币的铸币地址
    pub base_mint: String,
    /// 报价代币的铸币地址（通常是 SOL）
    pub quote_mint: String,
    /// 流动性池中基础代币的储备量（格式化后）
    pub pool_base_token_reserve: String,
    /// 流动性池中报价代币的储备量（格式化后）
    pub pool_quote_token_reserve: String,
    /// 计算出的代币价格（报价代币/基础代币）
    pub price: String,
    /// 原始基础代币储备量
    pub raw_base_reserve: u64,
    /// 原始报价代币储备量
    pub raw_quote_reserve: u64,
    /// 时间戳
    pub timestamp: u64,
}

/// AMM价格监控器
pub struct AmmPriceMonitor {
    /// RPC客户端
    rpc_client: HttpRpcClient,
}

impl AmmPriceMonitor {
    /// 创建新的AMM价格监控器
    pub fn new(rpc_client: HttpRpcClient) -> Self {
        info!("创建 AmmPriceMonitor");
        Self { rpc_client }
    }

    /// 获取池子的当前价格信息
    pub async fn get_pool_price(&self, pool_result: &PoolSearchResult) -> Result<AmmPriceOutput> {
        debug!("获取池子价格信息: {}", pool_result.pool_address);

        // 直接使用已知的池子数据，因为我们已经从搜索中获得了它
        let account_data = self.get_raw_account_data(&pool_result.pool_address.to_string()).await?;

        debug!("获取到原始账户数据，长度: {} bytes", account_data.len());

        // 将原始数据编码为base64，然后使用统一的解析功能
        use base64::Engine;
        let base64_data = base64::engine::general_purpose::STANDARD.encode(&account_data);

        // 使用新的统一解析功能
        use crate::pool_parser::{parse_account_data_by_length, AccountDataType};

        match parse_account_data_by_length(&base64_data, &pool_result.pool_address.to_string()) {
            Ok(AccountDataType::Pool(pool_parse_result)) => {
                debug!("✅ 成功解析为Pool账户");
                let pool = pool_parse_result.pool;

                debug!("池子信息:");
                debug!("  基础代币: {}", pool.base_mint);
                debug!("  报价代币: {}", pool.quote_mint);
                debug!("  LP供应量: {}", pool.lp_supply);

                // 获取基础代币和报价代币的账户信息来计算储备量
                let base_reserve = self.get_token_account_balance(&pool.pool_base_token_account).await?;
                let quote_reserve = self.get_token_account_balance(&pool.pool_quote_token_account).await?;

                debug!("储备量:");
                debug!("  基础代币储备: {}", base_reserve);
                debug!("  报价代币储备: {}", quote_reserve);

                // 计算价格
                let price_output = self.calculate_price_output(
                    &pool_result.pool_address.to_string(),
                    &pool.base_mint.to_string(),
                    &pool.quote_mint.to_string(),
                    base_reserve,
                    quote_reserve,
                )?;

                Ok(price_output)
            }

            Ok(AccountDataType::Unknown) => {
                debug!("❓ 无法识别账户类型，尝试传统解析方式");
                // 回退到传统的解析方式
                self.parse_pool_data_legacy(&account_data, pool_result).await
            }
            Err(e) => {
                debug!("❌ 解析账户数据失败: {}，尝试传统解析方式", e);
                // 回退到传统的解析方式
                self.parse_pool_data_legacy(&account_data, pool_result).await
            }
        }
    }

    /// 传统的池子数据解析方式（作为回退方案）
    async fn parse_pool_data_legacy(&self, account_data: &[u8], pool_result: &PoolSearchResult) -> Result<AmmPriceOutput> {
        debug!("使用传统方式解析池子数据");

        // 验证和解析池子数据
        const POOL_ACCOUNT_DISCM: [u8; 8] = [241, 154, 109, 4, 17, 177, 109, 188];

        if account_data.len() < 8 {
            return Err(anyhow!("账户数据长度不足，无法读取discriminator"));
        }

        // 检查discriminator
        let discriminator: [u8; 8] = account_data[..8].try_into()
            .map_err(|_| anyhow!("无法提取discriminator"))?;

        if discriminator != POOL_ACCOUNT_DISCM {
            return Err(anyhow!("账户discriminator不匹配，期望: {:?}, 实际: {:?}",
                POOL_ACCOUNT_DISCM, discriminator));
        }

        debug!("Discriminator验证成功，开始解析Pool数据");
        debug!("账户数据长度: {} bytes", account_data.len());

        // 跳过discriminator，解析Pool结构
        let pool = borsh::from_slice::<pumpfun_amm_interface::Pool>(&account_data[8..])
            .map_err(|e| anyhow!("解析AMM池子数据失败: {}", e))?;

        debug!("池子信息:");
        debug!("  基础代币: {}", pool.base_mint);
        debug!("  报价代币: {}", pool.quote_mint);
        debug!("  LP供应量: {}", pool.lp_supply);

        // 获取基础代币和报价代币的账户信息来计算储备量
        let base_reserve = self.get_token_account_balance(&pool.pool_base_token_account).await?;
        let quote_reserve = self.get_token_account_balance(&pool.pool_quote_token_account).await?;

        debug!("储备量:");
        debug!("  基础代币储备: {}", base_reserve);
        debug!("  报价代币储备: {}", quote_reserve);

        // 计算价格
        let price_output = self.calculate_price_output(
            &pool_result.pool_address.to_string(),
            &pool.base_mint.to_string(),
            &pool.quote_mint.to_string(),
            base_reserve,
            quote_reserve,
        )?;

        Ok(price_output)
    }

    /// 获取原始账户数据
    async fn get_raw_account_data(&self, account_address: &str) -> Result<Vec<u8>> {
        debug!("获取原始账户数据: {}", account_address);

        // TODO: 实现获取原始账户数据的逻辑
        return Err(anyhow!("获取原始账户数据功能待实现"));
    }

    /// 获取代币账户余额
    async fn get_token_account_balance(&self, token_account: &Pubkey) -> Result<u64> {
        debug!("获取代币账户余额: {}", token_account);

        let account_data = self.get_raw_account_data(&token_account.to_string()).await?;

        // SPL Token账户数据结构：前32字节是mint，接下来32字节是owner，然后8字节是amount
        if account_data.len() < 72 {
            return Err(anyhow!("代币账户数据长度不足"));
        }

        // 从字节64-72读取amount (u64, little endian)
        let amount_bytes: [u8; 8] = account_data[64..72].try_into()
            .map_err(|_| anyhow!("无法读取代币账户余额"))?;
        let amount = u64::from_le_bytes(amount_bytes);

        debug!("代币账户 {} 余额: {}", token_account, amount);
        Ok(amount)
    }

    /// 计算价格输出
    fn calculate_price_output(
        &self,
        pool_address: &str,
        base_mint: &str,
        quote_mint: &str,
        base_reserve: u64,
        quote_reserve: u64,
    ) -> Result<AmmPriceOutput> {
        const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
        
        // 默认精度（大多数代币使用6位小数）
        let decimal = 6u8;
        
        let (price, base_reserve_formatted, quote_reserve_formatted) = if base_mint == SOL_MINT {
            // 基础代币是SOL的情况
            let base_formatted = format!("{:.6} SOL", base_reserve as f64 / 1_000_000_000f64);
            let quote_formatted = (quote_reserve as f64 / 10f64.powi(decimal as i32)).to_string();
            let price = self.calculate_pump_amm_price(base_reserve, quote_reserve, decimal);
            (price, base_formatted, quote_formatted)
        } else {
            // 报价代币是SOL的情况（更常见）
            let base_formatted = (base_reserve as f64 / 10f64.powi(decimal as i32)).to_string();
            let quote_formatted = format!("{:.6} SOL", quote_reserve as f64 / 1_000_000_000f64);
            let price = self.calculate_pump_amm_price(quote_reserve, base_reserve, decimal);
            (price, base_formatted, quote_formatted)
        };

        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        Ok(AmmPriceOutput {
            pool_address: pool_address.to_string(),
            base_mint: base_mint.to_string(),
            quote_mint: quote_mint.to_string(),
            pool_base_token_reserve: base_reserve_formatted,
            pool_quote_token_reserve: quote_reserve_formatted,
            price: format!("{:.8} SOL", price),
            raw_base_reserve: base_reserve,
            raw_quote_reserve: quote_reserve,
            timestamp,
        })
    }

    /// 计算 PumpFun AMM 代币价格
    /// 
    /// 根据流动性池中的代币储备量计算代币的当前价格
    /// 
    /// # 参数
    /// * `pool_base_reserve` - 流动性池中基础代币的储备量（原始单位）
    /// * `pool_quote_reserve` - 流动性池中报价代币的储备量（原始单位）
    /// * `decimal` - 报价代币的小数位数
    /// 
    /// # 返回值
    /// * `f64` - 计算出的代币价格
    fn calculate_pump_amm_price(&self, pool_base_reserve: u64, pool_quote_reserve: u64, decimal: u8) -> f64 {
        // 将基础代币储备量从 lamports 转换为 SOL
        let base = pool_base_reserve as f64 / 1_000_000_000f64;
        // 将报价代币储备量根据精度调整
        let quote = pool_quote_reserve as f64 / 10f64.powi(decimal as i32);
        
        // 避免除零错误
        if quote == 0.0 {
            return 0.0;
        }
        
        // 计算价格比率
        base / quote
    }

    /// 开始监控池子价格变化
    pub async fn start_monitoring(&self, pool_result: &PoolSearchResult, interval_seconds: u64) -> Result<()> {
        info!("开始监控池子价格变化: {}", pool_result.pool_address);
        info!("监控间隔: {}秒", interval_seconds);

        let mut interval = tokio::time::interval(Duration::from_secs(interval_seconds));
        let mut previous_price: Option<f64> = None;

        loop {
            interval.tick().await;

            match self.get_pool_price(pool_result).await {
                Ok(price_output) => {
                    // 解析当前价格
                    let current_price_str = price_output.price.replace(" SOL", "");
                    if let Ok(current_price) = current_price_str.parse::<f64>() {
                        // 计算价格变化
                        if let Some(prev_price) = previous_price {
                            let change_percentage = ((current_price - prev_price) / prev_price) * 100.0;
                            let direction = if change_percentage > 0.0 { "📈" } else if change_percentage < 0.0 { "📉" } else { "➡️" };
                            
                            info!("{} 池子 {} 价格: {} (变化: {:.2}%)", 
                                direction,
                                price_output.pool_address,
                                price_output.price,
                                change_percentage
                            );
                        } else {
                            info!("🔍 池子 {} 初始价格: {}", 
                                price_output.pool_address,
                                price_output.price
                            );
                        }

                        info!("  基础代币储备: {}", price_output.pool_base_token_reserve);
                        info!("  报价代币储备: {}", price_output.pool_quote_token_reserve);
                        info!("  基础代币: {}", price_output.base_mint);
                        info!("  报价代币: {}", price_output.quote_mint);

                        previous_price = Some(current_price);
                    }
                }
                Err(e) => {
                    warn!("获取池子价格失败: {}", e);
                }
            }
        }
    }
}
