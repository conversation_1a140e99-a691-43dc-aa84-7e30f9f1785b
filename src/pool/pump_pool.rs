use anyhow::{Result, anyhow};

use log::{debug, info};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use pumpfun_amm_interface::Pool;

use crate::client::http_client::HttpRpcClient;
// 移除未使用的导入：AccountParser, AccountType
// use crate::parsers::AccountParser;
// use crate::pojo::solana::http::resp::AccountType;
use crate::pool::token_pool_finder::{TokenPoolFinder, PoolSearchResult, PoolType};

/// 代币信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub address: Pubkey,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
    pub balance: u64,
    pub balance_formatted: f64,
}

/// 池子信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PoolInfo {
    pub pool_address: Pubkey,
    pub program_id: Pubkey,
    pub token_a: TokenInfo,
    pub token_b: TokenInfo,
    pub liquidity: f64,
    pub price: f64,
    pub last_updated: u64,
}

/// 完整的池子数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolData {
    /// 基本池子信息
    pub pool_info: PoolInfo,
    /// 池子类型
    pub pool_type: PoolType,
    /// 基础代币储备量（原始值）
    pub base_token_reserve: u64,
    /// 报价代币储备量（原始值）
    pub quote_token_reserve: u64,
    /// 基础代币储备量（格式化后）
    pub base_token_reserve_formatted: f64,
    /// 报价代币储备量（格式化后）
    pub quote_token_reserve_formatted: f64,
    /// 当前价格（以报价代币计价）
    pub current_price: f64,
    /// LP代币总供应量
    pub lp_total_supply: u64,
}

/// Pump.fun 池子监控器
#[derive(Debug)]
pub struct PumpPoolMonitor {
    pool_address: Pubkey,
}

impl PumpPoolMonitor {
    /// 创建新的池子监控器
    pub fn new(pool_address: &str) -> Result<Self> {
        debug!("创建 PumpPoolMonitor，池子地址: {}", pool_address);

        let pool_address = Pubkey::from_str(pool_address)
            .map_err(|e| anyhow!("无效的池子地址: {}", e))?;

        debug!("池子监控器创建成功");

        Ok(Self {
            pool_address,
        })
    }

    /// 解析池子数据
    pub fn parse_pool_data(pool_data: &[u8]) -> Result<Pool> {
        debug!("开始解析 Pump AMM 池子数据，数据长度: {} bytes", pool_data.len());

        // 直接使用 Pool 进行解析
        let pool = borsh::from_slice::<Pool>(pool_data)
            .map_err(|e| anyhow!("解析池子数据失败: {}", e))?;

        debug!("池子数据解析成功:");
        debug!("  创建者: {}", pool.creator);
        debug!("  基础代币: {}", pool.base_mint);
        debug!("  报价代币: {}", pool.quote_mint);
        debug!("  基础代币账户: {}", pool.pool_base_token_account);
        debug!("  报价代币账户: {}", pool.pool_quote_token_account);
        debug!("  LP 供应量: {}", pool.lp_supply);

        Ok(pool)
    }

    /// 获取并解析池子数据
    pub async fn fetch_and_parse_pool_data(&self, rpc_client: &HttpRpcClient) -> Result<Pool> {
        debug!("获取池子数据: {}", self.pool_address);

        // TODO: 暂时注释，专注于第一步调试
        return Err(anyhow!("暂时注释掉，专注于第一步调试"));

        /*
        // 通过 RPC 获取账户信息
        let account_info = rpc_client.get_account_info(&self.pool_address.to_string()).await?
            .ok_or_else(|| anyhow!("池子账户不存在: {}", self.pool_address))?;

        // 检查账户所有者
        debug!("账户所有者: {}", account_info.owner);

        // 验证账户类型并获取 base64 数据
        AccountParser::validate_account_compatibility(&account_info, AccountType::PumpFunAmmPool)?;
        let base64_data = AccountParser::get_base64_data(&account_info)?;

        // 解码 base64 数据
        use base64::Engine;
        let pool_data = base64::engine::general_purpose::STANDARD.decode(&base64_data)
            .map_err(|e| anyhow!("解码 base64 数据失败: {}", e))?;

        // 解析池子数据
        Self::parse_pool_data(&pool_data)
        */
    }

    /// 验证池子数据格式
    pub fn validate_pool_data(pool_data: &[u8]) -> Result<bool> {
        debug!("验证池子数据格式，数据长度: {} bytes", pool_data.len());

        // 检查数据长度是否足够包含discriminator
        if pool_data.len() < 8 {
            return Ok(false);
        }

        // 检查discriminator是否匹配
        let discriminator = &pool_data[0..8];
        let expected_discriminator = [241, 154, 109, 4, 17, 177, 109, 188]; // POOL_ACCOUNT_DISCM

        let is_valid = discriminator == expected_discriminator;
        debug!("池子数据验证结果: {}", if is_valid { "有效" } else { "无效" });

        Ok(is_valid)
    }

    /// 获取池子地址
    pub fn get_pool_address(&self) -> &Pubkey {
        &self.pool_address
    }

    /// 根据token地址查找并获取池子数据
    pub async fn find_and_fetch_pool_by_token(
        token_address: &str,
        rpc_client: &HttpRpcClient,
    ) -> Result<Vec<PoolData>> {
        info!("根据token地址查找池子: {}", token_address);

        let mut finder = TokenPoolFinder::new();
        let discovery_result = finder.find_pools_by_token(token_address, rpc_client).await?;

        if discovery_result.pools.is_empty() {
            return Err(anyhow!("未找到token对应的池子: {}", token_address));
        }

        let mut pool_data_list = Vec::new();

        for pool_result in discovery_result.pools {
            match Self::fetch_pool_data_from_result(&pool_result, rpc_client).await {
                Ok(pool_data) => {
                    pool_data_list.push(pool_data);
                }
                Err(e) => {
                    debug!("获取池子数据失败: {}, 错误: {}", pool_result.pool_address, e);
                }
            }
        }

        if pool_data_list.is_empty() {
            return Err(anyhow!("所有找到的池子都无法获取数据"));
        }

        info!("成功获取 {} 个池子的数据", pool_data_list.len());
        Ok(pool_data_list)
    }

    /// 从池子搜索结果获取完整的池子数据
    pub async fn fetch_pool_data_from_result(
        pool_result: &PoolSearchResult,
        rpc_client: &HttpRpcClient,
    ) -> Result<PoolData> {
        debug!("获取池子数据: {}", pool_result.pool_address);

        // TODO: 暂时注释，专注于第一步调试
        return Err(anyhow!("暂时注释掉，专注于第一步调试"));

        /*
        // 获取池子账户信息
        let account_info = rpc_client.get_account_info(&pool_result.pool_address.to_string()).await?
            .ok_or_else(|| anyhow!("池子账户不存在: {}", pool_result.pool_address))?;

        // 验证账户类型并获取 base64 数据
        AccountParser::validate_account_compatibility(&account_info, AccountType::PumpFunAmmPool)?;
        let base64_data = AccountParser::get_base64_data(&account_info)?;

        // 解码数据
        use base64::Engine;
        let pool_data = base64::engine::general_purpose::STANDARD.decode(&base64_data)
            .map_err(|e| anyhow!("解码池子数据失败: {}", e))?;

        // 解析池子数据
        let raw_pool = Self::parse_pool_data(&pool_data)?;

        // 获取代币储备量
        let (base_reserve, quote_reserve) = Self::get_token_reserves(&raw_pool, rpc_client).await?;

        // 获取代币精度信息
        let (base_decimals, quote_decimals) = Self::get_token_decimals(&raw_pool, rpc_client).await?;

        // 计算格式化后的储备量
        let base_reserve_formatted = base_reserve as f64 / 10f64.powi(base_decimals as i32);
        let quote_reserve_formatted = quote_reserve as f64 / 10f64.powi(quote_decimals as i32);

        // 计算当前价格
        let current_price = if quote_reserve > 0 {
            (base_reserve as f64 / 10f64.powi(base_decimals as i32)) /
            (quote_reserve as f64 / 10f64.powi(quote_decimals as i32))
        } else {
            0.0
        };

        // 创建代币信息
        let token_a = TokenInfo {
            address: raw_pool.base_mint,
            symbol: "BASE".to_string(), // 需要从代币元数据获取
            name: "Base Token".to_string(),
            decimals: base_decimals,
            balance: base_reserve,
            balance_formatted: base_reserve_formatted,
        };

        let token_b = TokenInfo {
            address: raw_pool.quote_mint,
            symbol: if raw_pool.quote_mint.to_string() == "So11111111111111111111111111111111111111112" {
                "SOL".to_string()
            } else {
                "QUOTE".to_string()
            },
            name: "Quote Token".to_string(),
            decimals: quote_decimals,
            balance: quote_reserve,
            balance_formatted: quote_reserve_formatted,
        };

        // 创建池子信息
        let pool_info = PoolInfo {
            pool_address: pool_result.pool_address,
            program_id: pool_result.program_id,
            token_a,
            token_b,
            liquidity: base_reserve_formatted + quote_reserve_formatted, // 简化的流动性计算
            price: current_price,
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };

        let lp_supply = raw_pool.lp_supply;

        Ok(PoolData {
            pool_info,
            pool_type: pool_result.pool_type.clone(),
            base_token_reserve: base_reserve,
            quote_token_reserve: quote_reserve,
            base_token_reserve_formatted: base_reserve_formatted,
            quote_token_reserve_formatted: quote_reserve_formatted,
            current_price,
            lp_total_supply: lp_supply,
        })
        */
    }









    /// 从池子数据创建PoolInfo
    pub fn create_pool_info(pool: &Pool, pool_address: Pubkey, program_id: Pubkey) -> PoolInfo {
        debug!("创建 PoolInfo，池子地址: {}", pool_address);

        // 创建代币信息（这里使用默认值，实际使用时需要从其他地方获取代币元数据）
        let token_a = TokenInfo {
            address: pool.base_mint,
            symbol: "BASE".to_string(), // 需要从代币元数据获取
            name: "Base Token".to_string(), // 需要从代币元数据获取
            decimals: 6, // 需要从代币元数据获取
            balance: 0, // 需要从代币账户获取
            balance_formatted: 0.0,
        };

        let token_b = TokenInfo {
            address: pool.quote_mint,
            symbol: "QUOTE".to_string(), // 需要从代币元数据获取
            name: "Quote Token".to_string(), // 需要从代币元数据获取
            decimals: 9, // SOL 通常是 9 位小数
            balance: 0, // 需要从代币账户获取
            balance_formatted: 0.0,
        };

        PoolInfo {
            pool_address,
            program_id,
            token_a,
            token_b,
            liquidity: 0.0, // 需要计算
            price: 0.0, // 需要计算
            last_updated: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }
}

