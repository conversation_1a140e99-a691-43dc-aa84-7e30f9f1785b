use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 可序列化的订阅请求结构体
/// 对应 yellowstone_grpc_proto::prelude::SubscribeRequest
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequest {
    pub accounts: HashMap<String, SerializableSubscribeRequestFilterAccounts>,
    pub slots: HashMap<String, SerializableSubscribeRequestFilterSlots>,
    pub transactions: HashMap<String, SerializableSubscribeRequestFilterTransactions>,
    pub transactions_status: HashMap<String, SerializableSubscribeRequestFilterTransactionsStatus>,
    pub blocks: HashMap<String, SerializableSubscribeRequestFilterBlocks>,
    pub blocks_meta: HashMap<String, SerializableSubscribeRequestFilterBlocksMeta>,
    pub entry: HashMap<String, SerializableSubscribeRequestFilterEntry>,
    pub commitment: Option<String>,
    pub accounts_data_slice: Vec<SerializableSubscribeRequestAccountsDataSlice>,
    pub ping: Option<SerializableSubscribeRequestPing>,
    pub from_slot: Option<u64>,
}

/// 账户过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterAccounts {
    pub account: Vec<String>,
    pub owner: Vec<String>,
    pub filters: Vec<SerializableSubscribeRequestFilterAccountsFilter>,
    pub nonempty_txn_signature: Option<bool>,
}

/// 账户过滤器的具体过滤条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterAccountsFilter {
    pub filter: Option<SerializableSubscribeRequestFilterAccountsFilterType>,
}

/// 过滤器类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SerializableSubscribeRequestFilterAccountsFilterType {
    Memcmp(SerializableSubscribeRequestFilterAccountsFilterMemcmp),
    Datasize(u64),
    TokenAccountState,
}

/// 内存比较过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterAccountsFilterMemcmp {
    pub offset: u64,
    pub data: Option<SerializableSubscribeRequestFilterAccountsFilterMemcmpData>,
}

/// 内存比较数据类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SerializableSubscribeRequestFilterAccountsFilterMemcmpData {
    Bytes(Vec<u8>),
    Base58(String),
    Base64(String),
}

/// 插槽过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterSlots {
    pub filter_by_commitment: Option<bool>,
}

/// 交易过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterTransactions {
    pub vote: Option<bool>,
    pub failed: Option<bool>,
    pub signature: Option<String>,
    pub account_include: Vec<String>,
    pub account_exclude: Vec<String>,
    pub account_required: Vec<String>,
}

/// 交易状态过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterTransactionsStatus {
    pub signature: Option<String>,
}

/// 区块过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterBlocks {
    pub account_include: Vec<String>,
    pub include_transactions: Option<bool>,
    pub include_accounts: Option<bool>,
    pub include_entries: Option<bool>,
}

/// 区块元数据过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterBlocksMeta {
    // 目前为空结构体
}

/// 条目过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestFilterEntry {
    // 目前为空结构体
}

/// 账户数据切片
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestAccountsDataSlice {
    pub offset: u64,
    pub length: u64,
}

/// Ping 请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableSubscribeRequestPing {
    pub id: i32,
}

impl SerializableSubscribeRequest {
    /// 从原始的 SubscribeRequest 转换为可序列化版本
    pub fn from_grpc_request(request: &yellowstone_grpc_proto::prelude::SubscribeRequest) -> Self {
        use yellowstone_grpc_proto::prelude::CommitmentLevel;

        // 转换 commitment 枚举为字符串
        let commitment = request.commitment.map(|c| {
            match c {
                x if x == CommitmentLevel::Processed as i32 => "Processed".to_string(),
                x if x == CommitmentLevel::Confirmed as i32 => "Confirmed".to_string(),
                x if x == CommitmentLevel::Finalized as i32 => "Finalized".to_string(),
                _ => "Unknown".to_string(),
            }
        });

        Self {
            accounts: request.accounts.iter().map(|(k, v)| {
                (k.clone(), SerializableSubscribeRequestFilterAccounts {
                    account: v.account.clone(),
                    owner: v.owner.clone(),
                    filters: v.filters.iter().map(|_f| {
                        // 简化处理，因为我们主要使用交易过滤而不是账户过滤
                        SerializableSubscribeRequestFilterAccountsFilter {
                            filter: None,
                        }
                    }).collect(),
                    nonempty_txn_signature: v.nonempty_txn_signature,
                })
            }).collect(),

            slots: request.slots.iter().map(|(k, v)| {
                (k.clone(), SerializableSubscribeRequestFilterSlots {
                    filter_by_commitment: v.filter_by_commitment,
                })
            }).collect(),

            transactions: request.transactions.iter().map(|(k, v)| {
                (k.clone(), SerializableSubscribeRequestFilterTransactions {
                    vote: v.vote,
                    failed: v.failed,
                    signature: v.signature.clone(),
                    account_include: v.account_include.clone(),
                    account_exclude: v.account_exclude.clone(),
                    account_required: v.account_required.clone(),
                })
            }).collect(),

            transactions_status: request.transactions_status.iter().map(|(k, _v)| {
                (k.clone(), SerializableSubscribeRequestFilterTransactionsStatus {
                    signature: None, // 根据实际结构调整
                })
            }).collect(),

            blocks: request.blocks.iter().map(|(k, v)| {
                (k.clone(), SerializableSubscribeRequestFilterBlocks {
                    account_include: v.account_include.clone(),
                    include_transactions: v.include_transactions,
                    include_accounts: v.include_accounts,
                    include_entries: v.include_entries,
                })
            }).collect(),

            blocks_meta: request.blocks_meta.iter().map(|(k, _v)| {
                (k.clone(), SerializableSubscribeRequestFilterBlocksMeta {})
            }).collect(),

            entry: request.entry.iter().map(|(k, _v)| {
                (k.clone(), SerializableSubscribeRequestFilterEntry {})
            }).collect(),

            commitment,

            accounts_data_slice: request.accounts_data_slice.iter().map(|slice| {
                SerializableSubscribeRequestAccountsDataSlice {
                    offset: slice.offset,
                    length: slice.length,
                }
            }).collect(),

            ping: request.ping.as_ref().map(|p| {
                SerializableSubscribeRequestPing {
                    id: p.id,
                }
            }),

            from_slot: request.from_slot,
        }
    }
}
