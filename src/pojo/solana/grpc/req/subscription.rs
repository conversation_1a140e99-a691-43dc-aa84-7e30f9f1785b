use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// gRPC 订阅请求配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionConfig {
    pub commitment_level: CommitmentLevel,
    pub ping_interval_seconds: Option<u64>,
    pub from_slot: Option<u64>,
}

impl Default for SubscriptionConfig {
    fn default() -> Self {
        Self {
            commitment_level: CommitmentLevel::Confirmed,
            ping_interval_seconds: None,
            from_slot: None,
        }
    }
}

/// 承诺级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommitmentLevel {
    Processed,
    Confirmed,
    Finalized,
}

/// 交易过滤器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionFilterConfig {
    pub vote: Option<bool>,
    pub failed: Option<bool>,
    pub account_include: Vec<String>,
    pub account_exclude: Vec<String>,
    pub account_required: Vec<String>,
    pub signature: Option<String>,
}

impl Default for TransactionFilterConfig {
    fn default() -> Self {
        Self {
            vote: Some(false),
            failed: Some(false),
            account_include: vec![],
            account_exclude: vec![],
            account_required: vec![],
            signature: None,
        }
    }
}

/// 账户过滤器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountFilterConfig {
    pub account: Vec<String>,
    pub owner: Vec<String>,
    pub filters: Vec<AccountDataFilter>,
    pub nonempty_txn_signature: Option<String>,
}

impl Default for AccountFilterConfig {
    fn default() -> Self {
        Self {
            account: vec![],
            owner: vec![],
            filters: vec![],
            nonempty_txn_signature: None,
        }
    }
}

/// 账户数据过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountDataFilter {
    pub memcmp: Option<MemcmpFilter>,
    pub datasize: Option<u64>,
}

/// 内存比较过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemcmpFilter {
    pub offset: u64,
    pub bytes: String, // base58 encoded
}

/// 订阅请求构建器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionRequestBuilder {
    pub config: SubscriptionConfig,
    pub transaction_filters: HashMap<String, TransactionFilterConfig>,
    pub account_filters: HashMap<String, AccountFilterConfig>,
}

impl Default for SubscriptionRequestBuilder {
    fn default() -> Self {
        Self {
            config: SubscriptionConfig::default(),
            transaction_filters: HashMap::new(),
            account_filters: HashMap::new(),
        }
    }
}

impl SubscriptionRequestBuilder {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_commitment(mut self, level: CommitmentLevel) -> Self {
        self.config.commitment_level = level;
        self
    }

    pub fn with_ping_interval(mut self, seconds: u64) -> Self {
        self.config.ping_interval_seconds = Some(seconds);
        self
    }

    pub fn with_from_slot(mut self, slot: u64) -> Self {
        self.config.from_slot = Some(slot);
        self
    }

    pub fn add_transaction_filter(mut self, name: String, filter: TransactionFilterConfig) -> Self {
        self.transaction_filters.insert(name, filter);
        self
    }

    pub fn add_account_filter(mut self, name: String, filter: AccountFilterConfig) -> Self {
        self.account_filters.insert(name, filter);
        self
    }
}
