use serde::{Deserialize, Serialize};

/// gRPC 连接参数
#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct GrpcConnectionArgs {
    pub endpoint: String,
    pub x_token: Option<String>,
    pub timeout_seconds: u64,
}

impl Default for GrpcConnectionArgs {
    fn default() -> Self {
        Self {
            endpoint: "https://solana-yellowstone-grpc.publicnode.com:443".to_string(),
            x_token: None,
            timeout_seconds: 30,
        }
    }
}

/// gRPC 连接配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GrpcConnectionConfig {
    pub args: GrpcConnectionArgs,
    pub use_tls: bool,
    pub max_decoding_message_size: usize,
    pub connect_timeout_seconds: u64,
}

impl Default for GrpcConnectionConfig {
    fn default() -> Self {
        Self {
            args: GrpcConnectionArgs::default(),
            use_tls: true,
            max_decoding_message_size: 1024 * 1024 * 1024, // 1GB
            connect_timeout_seconds: 30,
        }
    }
}
