use serde::{Deserialize, Serialize};

/// gRPC 交易更新数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TransactionUpdateData {
    pub transaction: GrpcTransactionInfo,
    pub slot: u64,
    pub index: u64,
}

/// gRPC 交易信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GrpcTransactionInfo {
    pub signature: String,
    pub is_vote: bool,
    pub transaction: TransactionData,
    pub meta: Option<TransactionMeta>,
}

/// 交易数据
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TransactionData {
    pub signatures: Vec<String>,
    pub message: TransactionMessage,
}

/// 交易消息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TransactionMessage {
    pub header: MessageHeader,
    pub account_keys: Vec<String>,
    pub recent_blockhash: String,
    pub instructions: Vec<CompiledInstruction>,
    pub versioned: bool,
    pub address_table_lookups: Vec<AddressTableLookup>,
}

/// 消息头
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MessageHeader {
    pub num_required_signatures: u8,
    pub num_readonly_signed_accounts: u8,
    pub num_readonly_unsigned_accounts: u8,
}

/// 编译后的指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompiledInstruction {
    pub program_id_index: u8,
    pub accounts: Vec<u8>,
    pub data: Vec<u8>,
}

/// 地址表查找
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddressTableLookup {
    pub account_key: String,
    pub writable_indexes: Vec<u8>,
    pub readonly_indexes: Vec<u8>,
}

/// 交易元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionMeta {
    pub err: Option<TransactionError>,
    pub fee: u64,
    pub pre_balances: Vec<u64>,
    pub post_balances: Vec<u64>,
    pub inner_instructions: Vec<InnerInstructions>,
    pub log_messages: Vec<String>,
    pub pre_token_balances: Vec<TokenBalance>,
    pub post_token_balances: Vec<TokenBalance>,
    pub rewards: Vec<Reward>,
    pub loaded_addresses: LoadedAddresses,
    pub return_data: Option<ReturnData>,
    pub compute_units_consumed: Option<u64>,
}

/// 交易错误
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionError {
    pub error_type: String,
    pub error_detail: Option<String>,
}

/// 内部指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InnerInstructions {
    pub index: u8,
    pub instructions: Vec<CompiledInstruction>,
}

/// Token 余额
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenBalance {
    pub account_index: u8,
    pub mint: String,
    pub ui_token_amount: UiTokenAmount,
    pub owner: Option<String>,
    pub program_id: Option<String>,
}

/// UI Token 数量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiTokenAmount {
    pub ui_amount: Option<f64>,
    pub decimals: u8,
    pub amount: String,
    pub ui_amount_string: String,
}

/// 奖励
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Reward {
    pub pubkey: String,
    pub lamports: i64,
    pub post_balance: u64,
    pub reward_type: Option<String>,
    pub commission: Option<u8>,
}

/// 加载的地址
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadedAddresses {
    pub writable: Vec<String>,
    pub readonly: Vec<String>,
}

/// 返回数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReturnData {
    pub program_id: String,
    pub data: Vec<u8>,
}

/// 交易更新通知
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionUpdateNotification {
    pub signature: String,
    pub slot: u64,
    pub transaction: GrpcTransactionInfo,
    pub update_type: TransactionUpdateType,
}

/// 交易更新类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionUpdateType {
    Processed,
    Confirmed,
    Finalized,
}
