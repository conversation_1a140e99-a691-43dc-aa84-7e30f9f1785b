use serde::{Deserialize, Serialize};

/// gRPC 订阅更新类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SubscriptionUpdateType {
    Account,
    Transaction,
    Slot,
    Block,
    BlockMeta,
    Entry,
    Ping,
}

/// gRPC 订阅更新基础结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionUpdateBase {
    pub update_type: SubscriptionUpdateType,
    pub slot: u64,
    pub timestamp: Option<i64>,
}

/// gRPC 订阅更新包装器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionUpdate {
    pub base: SubscriptionUpdateBase,
    pub data: SubscriptionUpdateData,
}

/// 订阅更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SubscriptionUpdateData {
    Account(super::AccountUpdateData),
    Transaction(super::TransactionUpdateData),
    Slot(SlotUpdateData),
    Block(BlockUpdateData),
    BlockMeta(BlockMetaUpdateData),
    Entry(EntryUpdateData),
    Ping(PingUpdateData),
}

/// Slot 更新数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SlotUpdateData {
    pub slot: u64,
    pub parent: Option<u64>,
    pub status: SlotStatus,
}

/// Slot 状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SlotStatus {
    Processed,
    Confirmed,
    Finalized,
}

/// Block 更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockUpdateData {
    pub slot: u64,
    pub blockhash: String,
    pub rewards: Vec<RewardInfo>,
    pub block_time: Option<i64>,
    pub block_height: Option<u64>,
}

/// 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardInfo {
    pub pubkey: String,
    pub lamports: i64,
    pub post_balance: u64,
    pub reward_type: Option<String>,
    pub commission: Option<u8>,
}

/// Block Meta 更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockMetaUpdateData {
    pub slot: u64,
    pub blockhash: String,
    pub parent_slot: u64,
    pub parent_blockhash: String,
    pub executed_transaction_count: u64,
    pub entries_count: u64,
}

/// Entry 更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EntryUpdateData {
    pub slot: u64,
    pub index: u64,
    pub num_hashes: u64,
    pub hash: String,
    pub executed_transaction_count: u64,
}

/// Ping 更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PingUpdateData {
    pub id: u32,
}
