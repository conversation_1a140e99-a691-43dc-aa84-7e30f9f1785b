use serde::{Deserialize, Serialize};

/// gRPC 账户更新数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AccountUpdateData {
    pub account: GrpcAccountInfo,
    pub slot: u64,
    pub is_startup: bool,
}

/// gRPC 账户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcAccountInfo {
    pub pubkey: String,
    pub lamports: u64,
    pub owner: String,
    pub executable: bool,
    pub rent_epoch: u64,
    pub data: Vec<u8>,
    pub write_version: u64,
    pub txn_signature: Option<String>,
}

/// 账户更新通知
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountUpdateNotification {
    pub pubkey: String,
    pub account: GrpcAccountInfo,
    pub slot: u64,
    pub is_startup: bool,
    pub update_type: AccountUpdateType,
}

/// 账户更新类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AccountUpdateType {
    Created,
    Modified,
    Deleted,
}

/// 解析后的账户更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedAccountUpdate {
    pub notification: AccountUpdateNotification,
    pub parsed_data: Option<ParsedAccountData>,
}

/// 解析后的账户数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ParsedAccountData {
    PumpFunAmmPool(PumpFunAmmPoolData),
    Token(TokenAccountData),
    Unknown(Vec<u8>),
}

/// Pump.fun AMM 池数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PumpFunAmmPoolData {
    pub pool_bump: u8,
    pub index: u16,
    pub creator: String,
    pub base_mint: String,
    pub quote_mint: String,
    pub lp_mint: String,
    pub pool_base_token_account: String,
    pub pool_quote_token_account: String,
    pub lp_supply: u64,
    pub coin_creator: String,
}

/// Token 账户数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenAccountData {
    pub mint: String,
    pub owner: String,
    pub amount: u64,
    pub delegate: Option<String>,
    pub state: TokenAccountState,
    pub is_native: Option<u64>,
    pub delegated_amount: u64,
    pub close_authority: Option<String>,
}

/// Token 账户状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TokenAccountState {
    Uninitialized,
    Initialized,
    Frozen,
}
