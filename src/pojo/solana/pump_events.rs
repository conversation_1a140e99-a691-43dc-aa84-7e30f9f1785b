use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;
use serde::Serialize;

/// CreateEvent - pump.fun代币创建事件
/// 对应discriminator: [27, 114, 169, 77, 222, 235, 99, 118]
#[derive(Clone, Debug, PartialEq, BorshDeserialize, BorshSerialize, Serialize)]
pub struct CreateEvent {
    pub name: String,                    // 代币名称
    pub symbol: String,                  // 代币符号
    pub uri: String,                     // 元数据URI
    pub mint: Pubkey,                    // 代币地址
    pub bonding_curve: Pubkey,           // 绑定曲线地址（池子地址）
    pub user: Pubkey,                    // 创建用户
    pub creator: Pubkey,                 // 创建者
    pub timestamp: i64,                  // 时间戳
    pub virtual_token_reserves: u64,     // 虚拟代币储备
    pub virtual_sol_reserves: u64,       // 虚拟SOL储备
    pub real_token_reserves: u64,        // 实际代币储备
    pub token_total_supply: u64,         // 代币总供应量
}

/// TradeEvent - pump.fun交易事件
/// 对应discriminator: [189, 219, 127, 211, 78, 230, 97, 238]
#[derive(Clone, Debug, PartialEq, BorshDeserialize, BorshSerialize, Serialize)]
pub struct TradeEvent {
    pub mint: Pubkey,                    // 代币地址
    pub sol_amount: u64,                 // SOL数量
    pub token_amount: u64,               // 代币数量
    pub is_buy: bool,                    // 是否为买入操作
    pub user: Pubkey,                    // 交易用户
    pub timestamp: i64,                  // 时间戳
    pub virtual_sol_reserves: u64,       // 虚拟SOL储备
    pub virtual_token_reserves: u64,     // 虚拟代币储备
    pub real_sol_reserves: u64,          // 实际SOL储备
    pub real_token_reserves: u64,        // 实际代币储备
    pub fee_recipient: Pubkey,           // 手续费接收者
    pub fee_basis_points: u64,           // 手续费基点
    pub fee: u64,                        // 手续费金额
    pub creator: Pubkey,                 // 创建者
    pub creator_fee_basis_points: u64,   // 创建者手续费基点
    pub creator_fee: u64,                // 创建者手续费
}

/// 事件解析器
pub struct PumpEventParser;

impl PumpEventParser {
    /// 解析CreateEvent数据（从字节数组）
    /// 
    /// # 参数
    /// * `data` - 包含CPI头部和discriminator的完整数据
    /// 
    /// # 返回值
    /// * `Result<CreateEvent>` - 解析的CreateEvent
    pub fn parse_create_event(data: &[u8]) -> anyhow::Result<CreateEvent> {
        // 验证数据长度（至少需要16字节：8字节CPI头 + 8字节discriminator）
        if data.len() < 16 {
            return Err(anyhow::anyhow!("数据长度不足，需要至少16字节"));
        }

        // 验证discriminator
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let discriminator = &data[discriminator_start..discriminator_end];
        
        if discriminator != crate::constants::events::CREATE_EVENT_EVENT_DISCM {
            return Err(anyhow::anyhow!(
                "CreateEvent discriminator不匹配，期望: {:?}，实际: {:?}",
                crate::constants::events::CREATE_EVENT_EVENT_DISCM,
                discriminator
            ));
        }

        // 提取事件数据（跳过CPI头部和discriminator）
        let event_data = &data[16..];
        
        // 使用borsh反序列化
        let create_event = borsh::from_slice::<CreateEvent>(event_data)
            .map_err(|e| anyhow::anyhow!("CreateEvent borsh反序列化失败: {}", e))?;

        Ok(create_event)
    }

    /// 解析TradeEvent数据（从字节数组）
    /// 
    /// # 参数
    /// * `data` - 包含CPI头部和discriminator的完整数据
    /// 
    /// # 返回值
    /// * `Result<TradeEvent>` - 解析的TradeEvent
    pub fn parse_trade_event(data: &[u8]) -> anyhow::Result<TradeEvent> {
        // 验证数据长度（至少需要16字节：8字节CPI头 + 8字节discriminator）
        if data.len() < 16 {
            return Err(anyhow::anyhow!("数据长度不足，需要至少16字节"));
        }

        // 验证discriminator
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let discriminator = &data[discriminator_start..discriminator_end];
        
        if discriminator != crate::constants::events::TRADE_EVENT_EVENT_DISCM {
            return Err(anyhow::anyhow!(
                "TradeEvent discriminator不匹配，期望: {:?}，实际: {:?}",
                crate::constants::events::TRADE_EVENT_EVENT_DISCM,
                discriminator
            ));
        }

        // 提取事件数据（跳过CPI头部和discriminator）
        let event_data = &data[16..];
        
        // 使用borsh反序列化
        let trade_event = borsh::from_slice::<TradeEvent>(event_data)
            .map_err(|e| anyhow::anyhow!("TradeEvent borsh反序列化失败: {}", e))?;

        Ok(trade_event)
    }

    /// 检查是否为CreateEvent
    pub fn is_create_event(data: &[u8]) -> bool {
        if data.len() < 16 {
            return false;
        }
        
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let discriminator = &data[discriminator_start..discriminator_end];
        
        discriminator == crate::constants::events::CREATE_EVENT_EVENT_DISCM
    }

    /// 检查是否为TradeEvent
    pub fn is_trade_event(data: &[u8]) -> bool {
        if data.len() < 16 {
            return false;
        }
        
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let discriminator = &data[discriminator_start..discriminator_end];
        
        discriminator == crate::constants::events::TRADE_EVENT_EVENT_DISCM
    }
}
