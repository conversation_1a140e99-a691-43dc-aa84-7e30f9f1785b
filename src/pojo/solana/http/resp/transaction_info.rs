use serde::{Deserialize, Serialize};
use super::RpcError;

/// getTransaction RPC 响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetTransactionResponse {
    pub jsonrpc: String,
    pub id: u64,
    pub result: Option<TransactionInfo>,
    pub error: Option<RpcError>,
}

/// 交易信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TransactionInfo {
    /// 区块时间戳
    #[serde(rename = "blockTime")]
    pub block_time: Option<i64>,
    /// 交易元数据
    pub meta: Option<TransactionMeta>,
    /// 区块槽位
    pub slot: u64,
    /// 交易数据
    pub transaction: Option<Transaction>,
    /// 版本
    pub version: Option<serde_json::Value>,
}

/// 交易元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionMeta {
    /// 错误信息
    pub err: Option<serde_json::Value>,
    /// 手续费
    pub fee: u64,
    /// 内部指令
    #[serde(rename = "innerInstructions")]
    pub inner_instructions: Option<Vec<InnerInstruction>>,
    /// 日志消息
    #[serde(rename = "logMessages")]
    pub log_messages: Option<Vec<String>>,
    /// 账户余额变化前
    #[serde(rename = "preBalances")]
    pub pre_balances: Vec<u64>,
    /// 账户余额变化后
    #[serde(rename = "postBalances")]
    pub post_balances: Vec<u64>,
    /// Token余额变化前
    #[serde(rename = "preTokenBalances")]
    pub pre_token_balances: Option<Vec<TokenBalance>>,
    /// Token余额变化后
    #[serde(rename = "postTokenBalances")]
    pub post_token_balances: Option<Vec<TokenBalance>>,
    /// 奖励
    pub rewards: Option<Vec<Reward>>,
    /// 状态
    pub status: Option<serde_json::Value>,
}

/// 内部指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InnerInstruction {
    /// 指令索引
    pub index: u32,
    /// 指令列表
    pub instructions: Vec<CompiledInstruction>,
}

/// 编译后的指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompiledInstruction {
    /// 程序ID索引
    #[serde(rename = "programIdIndex")]
    pub program_id_index: u8,
    /// 账户索引
    pub accounts: Vec<u8>,
    /// 指令数据
    pub data: String,
}

/// Token余额
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenBalance {
    /// 账户索引
    #[serde(rename = "accountIndex")]
    pub account_index: u32,
    /// Mint地址
    pub mint: String,
    /// 所有者
    pub owner: Option<String>,
    /// 程序ID
    #[serde(rename = "programId")]
    pub program_id: Option<String>,
    /// UI Token数量
    #[serde(rename = "uiTokenAmount")]
    pub ui_token_amount: UiTokenAmount,
}

/// UI Token数量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiTokenAmount {
    /// 数量
    pub amount: String,
    /// 小数位数
    pub decimals: u8,
    /// UI数量
    #[serde(rename = "uiAmount")]
    pub ui_amount: Option<f64>,
    /// UI数量字符串
    #[serde(rename = "uiAmountString")]
    pub ui_amount_string: String,
}

/// 奖励
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Reward {
    /// 公钥
    pub pubkey: String,
    /// 奖励数量（lamports）
    pub lamports: i64,
    /// 奖励后余额
    #[serde(rename = "postBalance")]
    pub post_balance: u64,
    /// 奖励类型
    #[serde(rename = "rewardType")]
    pub reward_type: Option<String>,
    /// 佣金
    pub commission: Option<u8>,
}

/// 交易
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    /// 消息
    pub message: Message,
    /// 签名
    pub signatures: Vec<String>,
}

/// 消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 账户密钥
    #[serde(rename = "accountKeys")]
    pub account_keys: Vec<String>,
    /// 头部
    pub header: MessageHeader,
    /// 指令
    pub instructions: Vec<CompiledInstruction>,
    /// 最近区块哈希
    #[serde(rename = "recentBlockhash")]
    pub recent_blockhash: String,
}

/// 消息头部
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageHeader {
    /// 需要签名的只读账户数量
    #[serde(rename = "numReadonlySignedAccounts")]
    pub num_readonly_signed_accounts: u8,
    /// 不需要签名的只读账户数量
    #[serde(rename = "numReadonlyUnsignedAccounts")]
    pub num_readonly_unsigned_accounts: u8,
    /// 需要签名的账户数量
    #[serde(rename = "numRequiredSignatures")]
    pub num_required_signatures: u8,
}


