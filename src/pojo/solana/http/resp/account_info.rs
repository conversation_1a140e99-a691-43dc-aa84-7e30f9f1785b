use serde::{Deserialize, Serialize};

/// getAccountInfo 响应上下文
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RpcContext {
    #[serde(rename = "apiVersion")]
    pub api_version: String,
    pub slot: u64,
}

/// 解析后的 Token 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedTokenInfo {
    pub decimals: u8,
    #[serde(rename = "freezeAuthority")]
    pub freeze_authority: Option<String>,
    #[serde(rename = "isInitialized")]
    pub is_initialized: bool,
    #[serde(rename = "mintAuthority")]
    pub mint_authority: Option<String>,
    pub supply: String,
}

/// 解析后的账户数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedAccountData {
    pub info: ParsedTokenInfo,
    #[serde(rename = "type")]
    pub account_type: String,
}

/// jsonParsed 格式的数据结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct JsonParsedData {
    pub parsed: ParsedAccountData,
    pub program: String,
    pub space: u64,
}

/// 账户数据，支持 jsonParsed 和 base64 两种格式
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum AccountData {
    /// jsonParsed 格式（用于 Token 等已知程序）
    JsonParsed(JsonParsedData),
    /// base64 格式（用于未知程序或自定义程序）
    Base64(Vec<String>), // [data, encoding]
}

/// getAccountInfo 响应值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountValue {
    pub data: AccountData,
    pub executable: bool,
    pub lamports: u64,
    pub owner: String,
    #[serde(rename = "rentEpoch")]
    pub rent_epoch: u64,
    pub space: u64,
}

/// getAccountInfo 响应结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountInfoResult {
    pub context: RpcContext,
    pub value: Option<AccountValue>,
}

/// getAccountInfo 完整响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetAccountInfoResponse {
    pub jsonrpc: String,
    pub result: Option<AccountInfoResult>,
    pub id: u64,
}

/// 账户信息结构（保持向后兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountInfo {
    pub data: AccountData,
    pub executable: bool,
    pub lamports: u64,
    pub owner: String,
    #[serde(rename = "rentEpoch")]
    pub rent_epoch: u64,
}

/// 账户类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum AccountType {
    Token,
    PumpFunAmmPool,
    Unknown,
}
