use serde::{Deserialize, Serialize};

/// getSignaturesForAddress RPC 响应结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GetSignaturesResponse {
    pub jsonrpc: String,
    pub id: u64,
    pub result: Option<Vec<SignatureInfo>>,
    pub error: Option<RpcError>,
}

/// 交易签名信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignatureInfo {
    /// 交易签名
    pub signature: String,
    /// 区块槽位
    pub slot: u64,
    /// 错误信息（如果交易失败）
    pub err: Option<serde_json::Value>,
    /// 备注
    pub memo: Option<String>,
    /// 区块时间戳
    #[serde(rename = "blockTime")]
    pub block_time: Option<i64>,
    /// 确认状态
    #[serde(rename = "confirmationStatus")]
    pub confirmation_status: Option<String>,
}

/// RPC 错误信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RpcError {
    pub code: i32,
    pub message: String,
    pub data: Option<serde_json::Value>,
}
