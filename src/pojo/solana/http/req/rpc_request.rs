use serde::{Serialize};
use serde_json::Value;
use std::collections::HashMap;

/// HTTP 请求配置
#[derive(Debug, Clone)]
pub struct HttpRequestConfig {
    pub url: String,
    pub method: String,
    pub headers: HashMap<String, String>,
    pub body: Option<String>,
}

impl Default for HttpRequestConfig {
    fn default() -> Self {
        let mut headers = HashMap::new();
        headers.insert("Content-Type".to_string(), "application/json".to_string());

        Self {
            url: String::new(),
            method: "POST".to_string(),
            headers,
            body: None,
        }
    }
}

/// Solana RPC 请求结构
#[derive(Debug, Serialize)]
pub struct RpcRequest {
    pub jsonrpc: String,
    pub id: u64,
    pub method: String,
    pub params: Value,
}

impl RpcRequest {
    pub fn new(id: u64, method: &str, params: Value) -> Self {
        Self {
            jsonrpc: "2.0".to_string(),
            id,
            method: method.to_string(),
            params,
        }
    }
}
