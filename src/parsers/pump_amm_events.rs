use anyhow::{Result, anyhow};
use solana_sdk::pubkey::Pubkey;
// 移除未使用的导入：std::str::FromStr, log::debug
// use std::str::FromStr;
// use log::debug;
use borsh::{BorshDeserialize, BorshSerialize};

/// Pump.fun AMM 事件 discriminators
/// 这些值来自 pump_amm_0.1.0.json IDL 文件
pub struct EventDiscriminators;

impl EventDiscriminators {
    /// BuyEvent discriminator: [103, 244, 82, 31, 44, 245, 119, 119]
    pub const BUY_EVENT: [u8; 8] = [103, 244, 82, 31, 44, 245, 119, 119];
    
    /// SellEvent discriminator: [62, 47, 55, 10, 165, 3, 220, 42]
    pub const SELL_EVENT: [u8; 8] = [62, 47, 55, 10, 165, 3, 220, 42];
    
    /// CreatePoolEvent discriminator: [177, 49, 12, 210, 160, 118, 167, 116]
    pub const CREATE_POOL_EVENT: [u8; 8] = [177, 49, 12, 210, 160, 118, 167, 116];
    
    /// CollectCoinCreatorFeeEvent discriminator: [232, 245, 194, 238, 234, 218, 58, 89]
    pub const COLLECT_COIN_CREATOR_FEE_EVENT: [u8; 8] = [232, 245, 194, 238, 234, 218, 58, 89];
    
    /// DepositEvent discriminator: [120, 248, 61, 83, 31, 142, 107, 144]
    pub const DEPOSIT_EVENT: [u8; 8] = [120, 248, 61, 83, 31, 142, 107, 144];
    
    /// WithdrawEvent discriminator: [22, 9, 133, 26, 160, 44, 71, 192]
    pub const WITHDRAW_EVENT: [u8; 8] = [22, 9, 133, 26, 160, 44, 71, 192];
}

/// BuyEvent 结构体
/// 对应 IDL 中的 BuyEvent 定义
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct BuyEvent {
    pub timestamp: i64,
    pub base_amount_out: u64,
    pub max_quote_amount_in: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_in: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_in_with_lp_fee: u64,
    pub user_quote_amount_in: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}

/// SellEvent 结构体
/// 对应 IDL 中的 SellEvent 定义
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct SellEvent {
    pub timestamp: i64,
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_out: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_out_without_lp_fee: u64,
    pub user_quote_amount_out: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}

/// CreatePoolEvent 结构体
/// 对应 IDL 中的 CreatePoolEvent 定义
#[derive(Debug, Clone, BorshDeserialize, BorshSerialize)]
pub struct CreatePoolEvent {
    pub timestamp: i64,
    pub index: u16,
    pub creator: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub base_mint_decimals: u8,
    pub quote_mint_decimals: u8,
    pub base_amount_in: u64,
    pub quote_amount_in: u64,
    pub pool_base_amount: u64,
    pub pool_quote_amount: u64,
    pub minimum_liquidity: u64,
    pub initial_liquidity: u64,
    pub lp_token_amount_out: u64,
    pub pool_bump: u8,
    pub pool: Pubkey,
    pub lp_mint: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub coin_creator: Pubkey,
}

/// Pump.fun AMM 事件类型枚举
#[derive(Debug, Clone)]
pub enum PumpAmmEvent {
    Buy(BuyEvent),
    Sell(SellEvent),
    CreatePool(CreatePoolEvent),
    Unknown(Vec<u8>),
}

/// 事件解析器
pub struct EventParser;

impl EventParser {
    /// 第一步：验证是否为CPI日志
    pub fn is_cpi_log(data: &[u8]) -> bool {
        use crate::constants::cpi::ANCHOR_SELF_CPI_LOG;

        if data.len() < 8 {
            return false;
        }

        // 检查数据开头是否匹配CPI标识符
        data.starts_with(&ANCHOR_SELF_CPI_LOG)
    }

    /// 第二步：判断是否为SellEvent（检查discriminator）
    pub fn is_sell_event(data: &[u8]) -> bool {
        if data.len() < 16 {  // 至少需要8字节CPI头 + 8字节discriminator
            return false;
        }

        // 跳过CPI头部（8字节），检查discriminator
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let discriminator = &data[discriminator_start..discriminator_end];

        discriminator == EventDiscriminators::SELL_EVENT
    }

    /// 第三步：反序列化SellEvent数据
    pub fn deserialize_sell_event(data: &[u8]) -> Result<SellEvent> {
        if data.len() < 16 {
            return Err(anyhow!("数据长度不足"));
        }

        // 跳过CPI头部（8字节）和discriminator（8字节）
        let event_data = &data[16..];

        // 反序列化事件数据
        let event = SellEvent::try_from_slice(event_data)?;
        Ok(event)
    }

    // 为了兼容其他代码，添加这些方法
    pub fn identify_event_type(discriminator: &[u8; 8]) -> Option<&'static str> {
        match *discriminator {
            EventDiscriminators::BUY_EVENT => Some("BuyEvent"),
            EventDiscriminators::SELL_EVENT => Some("SellEvent"),
            EventDiscriminators::CREATE_POOL_EVENT => Some("CreatePoolEvent"),
            EventDiscriminators::COLLECT_COIN_CREATOR_FEE_EVENT => Some("CollectCoinCreatorFeeEvent"),
            EventDiscriminators::DEPOSIT_EVENT => Some("DepositEvent"),
            EventDiscriminators::WITHDRAW_EVENT => Some("WithdrawEvent"),
            _ => None,
        }
    }

    pub fn parse_event(data: &[u8]) -> Result<PumpAmmEvent> {
        if data.len() < 8 {
            return Err(anyhow!("事件数据长度不足，至少需要8字节discriminator"));
        }

        // 提取 discriminator（前8字节）
        let mut discriminator = [0u8; 8];
        discriminator.copy_from_slice(&data[0..8]);

        // 根据 discriminator 确定事件类型并解析
        match discriminator {
            EventDiscriminators::BUY_EVENT => {
                let event = BuyEvent::try_from_slice(&data[8..])?;
                Ok(PumpAmmEvent::Buy(event))
            }
            EventDiscriminators::SELL_EVENT => {
                let event = SellEvent::try_from_slice(&data[8..])?;
                Ok(PumpAmmEvent::Sell(event))
            }
            EventDiscriminators::CREATE_POOL_EVENT => {
                let event = CreatePoolEvent::try_from_slice(&data[8..])?;
                Ok(PumpAmmEvent::CreatePool(event))
            }
            _ => Ok(PumpAmmEvent::Unknown(data.to_vec())),
        }
    }

    pub fn extract_pool_address(event: &PumpAmmEvent) -> Option<Pubkey> {
        match event {
            PumpAmmEvent::Buy(buy_event) => Some(buy_event.pool),
            PumpAmmEvent::Sell(sell_event) => Some(sell_event.pool),
            PumpAmmEvent::CreatePool(create_pool_event) => Some(create_pool_event.pool),
            PumpAmmEvent::Unknown(_) => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_three_step_parsing() {
        // 您提供的完整data
        let data_str = "9k6unfwB8yYie7YGjfXzMueMgxc3tp5VMmZkwYko69Lw6ywghanrQWrdyTj921qZc6kXkUpHkpghL7Lh44DYYCYJpKRLVc6LaZrpWAcuDLyDWt8kJmEvWCq3rPmUNwp6jqKifqQHpAAoY3UCu7EUGoXZULgoeLpwgx1z5skM7bGvjLqzgLAchiWmju1F1bmESUL44CpFdecNfe6YErZba15PXJu1VdhfA4yHgTGiNiNDiUvEijsRjddoHpzKfnzSfELxycPvwwen4foBigMzei9yJXGsBB1NvDJZC77NMzQvKtU74aziys7SL3VosvDsUUUrNWByitJpJwtjg5V3WjGFMRBC9yrSgpVyy6xY3FY4sAxcL3nUFGDS1LUv5eG6Wy7kRgJr2kNP8nTrXPE5H3BEsUE7x4BXzM6su5aXmiuBMZ59vK4D754WRP5crQETNoYdhGJVZetBAswrfxALjrqz8apap6gwijdshjPxWk9SyQDVP41znFy";

        // 将base58字符串解码为字节数组
        let data = match bs58::decode(data_str).into_vec() {
            Ok(bytes) => bytes,
            Err(e) => {
                panic!("Base58解码失败: {}", e);
            }
        };

        println!("🔍 开始三步解析流程");
        println!("输入数据长度: {}", data.len());
        println!("输入数据前32字节: {:?}", &data[..32.min(data.len())]);

        // 第一步：验证是否为CPI日志
        println!("\n📋 第一步：验证是否为CPI日志");
        let is_cpi_log = EventParser::is_cpi_log(&data);
        println!("是否为CPI日志: {}", is_cpi_log);

        // 第二步：判断是否为SellEvent
        println!("\n🔍 第二步：判断是否为SellEvent");
        let is_sell = EventParser::is_sell_event(&data);
        println!("是否为SellEvent: {}", is_sell);

        // 第三步：反序列化SellEvent数据
        println!("\n⚙️ 第三步：反序列化SellEvent数据");
        if is_cpi_log && is_sell {
            match EventParser::deserialize_sell_event(&data) {
                Ok(sell_event) => {
                    println!("✅ SellEvent 反序列化成功！");
                    println!("SellEvent 详细信息:");
                    println!("{:#?}", sell_event);
                }
                Err(e) => {
                    println!("❌ SellEvent 反序列化失败: {}", e);
                }
            }
        } else {
            println!("❌ 数据验证失败，无法进行反序列化");
        }
    }



}
