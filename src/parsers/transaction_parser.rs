use anyhow::Result;
use log::{debug, info, warn};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use base64::{Engine as _, engine::general_purpose};

use crate::pojo::solana::http::resp::TransactionInfo;
use crate::pool::token_pool_finder::{PoolSearchResult, PoolType};
use crate::constants;
use crate::constants::cpi::ANCHOR_SELF_CPI_LOG;
use crate::parsers::pump_amm_events::EventParser;



/// 交易解析器
/// 专门负责解析交易数据并提取 CPI LOG("e445a52e51cb9a1d") 信息，从中获取池子地址
pub struct TransactionParser;

impl TransactionParser {
    /// 创建新的交易解析器
    pub fn new() -> Self {
        Self
    }

    /// 从交易信息中解析出 pump.fun 池子地址
    /// 
    /// # 参数
    /// * `transaction_info` - 交易信息
    /// * `target_token` - 目标token地址
    /// 
    /// # 返回值
    /// * `Result<Vec<PoolSearchResult>>` - 找到的池子列表
    pub fn extract_pump_pools_from_transaction(
        &self,
        transaction_info: &TransactionInfo,
        target_token: &str,
    ) -> Result<Vec<PoolSearchResult>> {
        debug!("=== 开始解析交易以提取pump.fun池子 ===");
        debug!("目标token: {}", target_token);

        let mut pools = Vec::new();

        // 1. 检查交易是否包含 pump.fun AMM 程序
        if !self.contains_pump_amm_program(transaction_info)? {
            debug!("交易不包含pump.fun AMM程序，跳过");
            return Ok(pools);
        }

        // 2. 从日志消息中提取 CPI LOG 信息
        if let Some(cpi_pools) = self.extract_pools_from_cpi_logs(transaction_info, target_token)? {
            pools.extend(cpi_pools);
        }



        info!("从交易中提取到 {} 个pump.fun池子", pools.len());
        for pool in &pools {
            info!("  - 池子地址: {}", pool.pool_address);
        }

        Ok(pools)
    }

    /// 检查交易是否包含 pump.fun 相关程序 (包括原始程序和AMM程序)
    fn contains_pump_amm_program(&self, transaction_info: &TransactionInfo) -> Result<bool> {
        if let Some(transaction) = &transaction_info.transaction {
            let account_keys = &transaction.message.account_keys;
            for account_key in account_keys {
                if account_key == constants::programs::PUMP_AMM_PROGRAM_ID {
                    debug!("✅ 交易包含pump.fun AMM程序: {}", account_key);
                    return Ok(true);
                } else if account_key == constants::programs::PUMP_FUN_PROGRAM_ID {
                    debug!("✅ 交易包含pump.fun原始程序: {}", account_key);
                    return Ok(true);
                } else if account_key == constants::programs::MINT_AUTHORITY {
                    debug!("✅ 交易包含pump.fun MINT_AUTHORITY程序: {}", account_key);
                    return Ok(true);
                }
            }
        }
        Ok(false)
    }

    /// 从 CPI inner instructions 和交易日志中提取池子地址
    /// 查找inner instructions中包含 "e445a52e51cb9a1d" 的data并解析事件数据
    /// 同时检查交易日志中的 "Program data:" 条目
    fn extract_pools_from_cpi_logs(
        &self,
        transaction_info: &TransactionInfo,
        target_token: &str,
    ) -> Result<Option<Vec<PoolSearchResult>>> {
        debug!("=== 从CPI inner instructions和交易日志中提取池子地址 ===");

        // 1. 首先检查交易日志中的 "Program data:" 条目
        if let Some(meta) = &transaction_info.meta {
            if let Some(log_messages) = &meta.log_messages {
                for (i, log_message) in log_messages.iter().enumerate() {
                    if log_message.starts_with("Program data: ") {
                        let data_part = &log_message[14..]; // 移除 "Program data: " 前缀
                        debug!("检查交易日志 {}: Program data: {}", i, data_part);

                        // 解析CPI数据提取池子地址
                        if let Some(pool_result) = self.parse_cpi_log_data(data_part, target_token)? {
                            return Ok(Some(vec![pool_result]));
                        }
                    }
                }
            }

            // 2. 如果交易日志中没找到，再检查inner instructions
            if let Some(inner_instructions) = &meta.inner_instructions {
                for (group_idx, inner_group) in inner_instructions.iter().enumerate() {
                    for (inst_idx, instruction) in inner_group.instructions.iter().enumerate() {
                        debug!("检查指令 {}-{}: program_id_index={}", group_idx, inst_idx, instruction.program_id_index);

                        // 检查指令的data字段
                        let data = &instruction.data;

                        // 解析CPI数据提取池子地址
                        if let Some(pool_result) = self.parse_cpi_log_data(data, target_token)? {
                            return Ok(Some(vec![pool_result]));
                        }
                    }
                }
            }
        }

        debug!("未在CPI inner instructions和交易日志中找到池子地址");
        Ok(None)
    }

    /// 解析 CPI instruction data 提取池子地址
    /// 直接检查data中是否包含CREATE_EVENT_EVENT_DISCM
    fn parse_cpi_log_data(
        &self,
        instruction_data: &str,
        target_token: &str,
    ) -> Result<Option<PoolSearchResult>> {
        debug!("解析CPI instruction数据: {}", instruction_data);

        // 1. 先解码数据，然后检查是否包含CREATE_EVENT_EVENT_DISCM
        if let Ok(decoded_data) = self.decode_event_data(instruction_data) {
            // 检查是否包含CREATE_EVENT_EVENT_DISCM (跳过CPI头部8字节后检查)
            if decoded_data.len() >= 16 {
                let discriminator_start = 8;
                let discriminator_end = discriminator_start + 8;
                let discriminator = &decoded_data[discriminator_start..discriminator_end];

                if discriminator == constants::events::CREATE_EVENT_EVENT_DISCM {
                    debug!("✅ 发现CREATE_EVENT_EVENT_DISCM，开始解析CreateEvent");

                    // 2. 使用CPI池子提取器解析CreateEvent
                    if let Ok(create_event) = crate::services::cpi_pool_extractor::parse_create_event_data(instruction_data) {
                        debug!("✅ 成功解析CreateEvent: {:?}", create_event);

                        // 3. 从CreateEvent中提取池子地址 (bonding_curve就是池子地址)
                        let pool_result = PoolSearchResult {
                            pool_address: create_event.bonding_curve,
                            program_id: Pubkey::from_str(constants::programs::PUMP_AMM_PROGRAM_ID).unwrap(),
                            base_mint: create_event.mint,
                            quote_mint: Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(), // SOL
                            pool_type: PoolType::PumpAMM,
                        };

                        info!("✅ 从CreateEvent中提取到池子地址: {}", create_event.bonding_curve);
                        return Ok(Some(pool_result));
                    } else {
                        warn!("❌ 解析CreateEvent失败");
                    }
                }
            }
        }

        // 如果不包含CREATE_EVENT_EVENT_DISCM，尝试原有逻辑
        if let Ok(decoded_data) = self.decode_event_data(instruction_data) {
            debug!("成功解码instruction数据，长度: {}", decoded_data.len());

            // 2. 检查是否包含CPI事件标识符 "e445a52e51cb9a1d"
            if self.contains_cpi_identifier(&decoded_data) {
                debug!("找到CPI事件标识符");

                // 3. 直接使用完整的decoded_data进行三步解析
                // 不需要提取CPI标识符后的数据，因为三步解析流程需要完整的CPI LOG数据
                if let Some(pool_result) = self.parse_event_by_discriminator(&[], &decoded_data, target_token)? {
                    return Ok(Some(pool_result));
                }
            } else {
                debug!("instruction数据中未找到CPI事件标识符");
            }
        } else {
            debug!("无法解码instruction数据");
        }

        Ok(None)
    }

    /// 检查数据中是否包含CPI事件标识符
    fn contains_cpi_identifier(&self, data: &[u8]) -> bool {
        // 检查数据长度是否足够
        if data.len() < ANCHOR_SELF_CPI_LOG.len() {
            debug!("数据长度 {} 小于CPI标识符长度 {}，跳过", data.len(), ANCHOR_SELF_CPI_LOG.len());
            return false;
        }

        // 在数据中查找CPI标识符
        for i in 0..=data.len().saturating_sub(ANCHOR_SELF_CPI_LOG.len()) {
            if &data[i..i + ANCHOR_SELF_CPI_LOG.len()] == ANCHOR_SELF_CPI_LOG {
                debug!("在位置 {} 找到CPI标识符", i);
                return true;
            }
        }
        false
    }



    /// 根据discriminator解析事件
    /// 使用三步解析流程处理CPI LOG数据
    fn parse_event_by_discriminator(
        &self,
        _discriminator: &[u8],
        full_cpi_data: &[u8],
        target_token: &str,
    ) -> Result<Option<PoolSearchResult>> {
        debug!("🔍 使用三步解析流程处理CPI LOG数据，数据长度: {}", full_cpi_data.len());

        // 第一步：验证是否为CPI日志
        if !EventParser::is_cpi_log(full_cpi_data) {
            debug!("❌ 第一步：数据不是有效的CPI日志");
            return Ok(None);
        }
        debug!("✅ 第一步：确认为CPI日志");

        // 第二步：判断是否为SellEvent（目前只处理SellEvent）
        if !EventParser::is_sell_event(full_cpi_data) {
            debug!("❌ 第二步：不是SellEvent，跳过处理");
            return Ok(None);
        }
        debug!("✅ 第二步：确认为SellEvent");

        // 第三步：反序列化SellEvent数据
        match EventParser::deserialize_sell_event(full_cpi_data) {
            Ok(sell_event) => {
                info!("🎉 第三步：成功解析SellEvent！");
                info!("SellEvent详细信息:");
                info!("  - timestamp: {}", sell_event.timestamp);
                info!("  - base_amount_in: {}", sell_event.base_amount_in);
                info!("  - quote_amount_out: {}", sell_event.quote_amount_out);
                info!("  - pool: {}", sell_event.pool);
                info!("  - user: {}", sell_event.user);

                // 从SellEvent中提取池子地址
                let pool_address = sell_event.pool;
                info!("🎯 从SellEvent中提取到池子地址: {}", pool_address);

                // 创建池子搜索结果
                let pool_result = PoolSearchResult {
                    pool_address,
                    program_id: Pubkey::from_str(crate::constants::programs::PUMP_AMM_PROGRAM_ID)?,
                    base_mint: Pubkey::from_str(target_token)?,
                    quote_mint: Pubkey::from_str("So11111111111111111111111111111111111111112")?, // SOL
                    pool_type: PoolType::PumpAMM,
                };

                info!("✅ 成功创建池子搜索结果: {:?}", pool_result);
                return Ok(Some(pool_result));
            }
            Err(e) => {
                debug!("❌ 第三步：解析SellEvent失败: {}", e);
            }
        }

        Ok(None)
    }



    /// 解码事件数据
    /// 尝试多种解码方式：base58、base64、十六进制等
    fn decode_event_data(&self, data_str: &str) -> Result<Vec<u8>> {
        let trimmed = data_str.trim();

        // 1. 尝试base58解码（Solana instruction data通常是base58编码）
        if let Ok(decoded) = bs58::decode(trimmed).into_vec() {
            debug!("成功使用base58解码，数据长度: {}", decoded.len());
            return Ok(decoded);
        }

        // 2. 尝试base64解码
        if let Ok(decoded) = general_purpose::STANDARD.decode(trimmed) {
            debug!("成功使用base64解码，数据长度: {}", decoded.len());
            return Ok(decoded);
        }

        // 3. 尝试十六进制解码
        if let Ok(decoded) = hex::decode(trimmed) {
            debug!("成功使用十六进制解码，数据长度: {}", decoded.len());
            return Ok(decoded);
        }

        // 4. 如果都失败，尝试将字符串作为原始字节
        debug!("无法解码数据，使用原始字节");
        Ok(trimmed.as_bytes().to_vec())
    }




}
