use anyhow::{Result, anyhow};
use log::{debug, info};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// 配置文件结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub rpc: RpcConfig,
    pub grpc: GrpcConfig,
    pub monitoring: MonitoringConfig,
    pub logging: LoggingConfig,
    pub proxy: ProxyConfig,
    pub network: NetworkConfig,
    pub database: DatabaseConfig,
}

/// HTTP RPC 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RpcConfig {
    pub endpoint: String,
    pub timeout_seconds: u64,
}

/// gRPC 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcConfig {
    pub endpoint: String,
    pub timeout_seconds: u64,
    pub use_tls: bool,
    pub x_token: Option<String>,
}

/// 监控配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 要监控的token地址列表
    pub token_addresses: Vec<String>,
    /// 监控间隔时间（秒）
    pub monitor_interval_seconds: u64,
    /// 价格变化阈值（百分比）
    pub price_change_threshold: f64,
    /// 是否启用实时流监听
    pub enable_stream_monitoring: bool,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub console: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub file_path: Option<String>,
}

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub enabled: bool,
    pub host: String,
    pub port: u16,
    pub username: Option<String>,
    pub password: Option<String>,
}



/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub max_retry_attempts: u32,
    pub retry_interval_ms: u64,
    pub connection_timeout_ms: u64,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库文件路径
    pub path: String,
    /// 是否启用数据库
    pub enabled: bool,
}

impl Config {
    /// 从文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        debug!("加载配置文件: {:?}", path);

        if !path.exists() {
            return Err(anyhow!("配置文件不存在: {:?}", path));
        }

        let content = fs::read_to_string(path).map_err(|e| anyhow!("读取配置文件失败: {}", e))?;

        debug!("配置文件内容长度: {} 字符", content.len());

        let config: Config =
            toml::from_str(&content).map_err(|e| anyhow!("解析配置文件失败: {}", e))?;

        info!("配置文件加载成功");
        debug!("配置详情: {:?}", config);

        Ok(config)
    }
}
