use anyhow::Result;
use log::debug;

use crate::client::http_client::HttpRpcClient;
use crate::pojo::solana::http::resp::{GetSignaturesResponse, SignatureInfo, GetTransactionResponse, TransactionInfo};

/// Solana RPC 服务
/// 专门负责调用 Solana RPC 方法，包括 getSignaturesForAddress 和 getTransaction
pub struct SolanaRpcService {
    /// HTTP RPC 客户端
    rpc_client: HttpRpcClient,
}

impl SolanaRpcService {
    /// 创建新的 Solana RPC 服务
    pub fn new(rpc_client: HttpRpcClient) -> Self {
        Self { rpc_client }
    }

    /// 获取指定地址的交易签名列表
    /// 
    /// # 参数
    /// * `address` - 要查询的地址
    /// * `limit` - 返回的最大签名数量（可选，默认1000）
    /// * `before` - 在此签名之前的交易（可选）
    /// * `until` - 直到此签名的交易（可选）
    /// 
    /// # 返回值
    /// * `Result<Vec<SignatureInfo>>` - 签名信息列表
    pub async fn get_signatures_for_address(
        &self,
        address: &str,
        limit: Option<usize>,
        before: Option<&str>,
        until: Option<&str>
    ) -> Result<Vec<SignatureInfo>> {
        debug!("=== getSignaturesForAddress RPC 调用 ===");
        debug!("地址: {}", address);
        debug!("限制数量: {:?}", limit);
        debug!("before: {:?}", before);
        debug!("until: {:?}", until);

        // 构建参数
        let mut params = serde_json::json!([address]);
        
        // 添加配置参数
        let mut config = serde_json::Map::new();
        if let Some(limit) = limit {
            config.insert("limit".to_string(), serde_json::Value::Number(serde_json::Number::from(limit)));
        }
        if let Some(before) = before {
            config.insert("before".to_string(), serde_json::Value::String(before.to_string()));
        }
        if let Some(until) = until {
            config.insert("until".to_string(), serde_json::Value::String(until.to_string()));
        }
        
        if !config.is_empty() {
            params.as_array_mut().unwrap().push(serde_json::Value::Object(config));
        }

        debug!("请求参数: {}", params);

        // 发送RPC请求
        let response: GetSignaturesResponse = self.rpc_client.send_rpc_request("getSignaturesForAddress", params).await?;

        debug!("=== getSignaturesForAddress 响应详情 ===");
        debug!("响应: {:?}", response);
        debug!("========================================");

        if let Some(result) = response.result {
            Ok(result)
        } else {
            Ok(Vec::new())
        }
    }

    /// 根据交易签名获取完整的交易信息
    /// 
    /// # 参数
    /// * `signature` - 交易签名
    /// * `encoding` - 编码格式（可选，默认"json"）
    /// * `commitment` - 确认级别（可选）
    /// * `max_supported_transaction_version` - 支持的最大交易版本（可选）
    /// 
    /// # 返回值
    /// * `Result<Option<TransactionInfo>>` - 交易信息（如果存在）
    pub async fn get_transaction(
        &self,
        signature: &str,
        encoding: Option<&str>,
        commitment: Option<&str>,
        max_supported_transaction_version: Option<u8>
    ) -> Result<Option<TransactionInfo>> {
        debug!("=== getTransaction RPC 调用 ===");
        debug!("交易签名: {}", signature);
        debug!("编码格式: {:?}", encoding);
        debug!("确认级别: {:?}", commitment);
        debug!("最大支持交易版本: {:?}", max_supported_transaction_version);

        // 构建参数
        let mut params = serde_json::json!([signature]);
        
        // 添加配置参数
        let mut config = serde_json::Map::new();
        config.insert("encoding".to_string(), serde_json::Value::String(encoding.unwrap_or("json").to_string()));
        
        if let Some(commitment) = commitment {
            config.insert("commitment".to_string(), serde_json::Value::String(commitment.to_string()));
        }
        
        if let Some(version) = max_supported_transaction_version {
            config.insert("maxSupportedTransactionVersion".to_string(), serde_json::Value::Number(serde_json::Number::from(version)));
        }
        
        params.as_array_mut().unwrap().push(serde_json::Value::Object(config));

        debug!("请求参数: {}", params);

        // 发送RPC请求
        let response: GetTransactionResponse = self.rpc_client.send_rpc_request("getTransaction", params).await?;

        Ok(response.result)
    }

    /// 批量获取交易信息
    /// 
    /// # 参数
    /// * `signatures` - 交易签名列表
    /// * `encoding` - 编码格式（可选，默认"json"）
    /// * `commitment` - 确认级别（可选）
    /// * `max_supported_transaction_version` - 支持的最大交易版本（可选）
    /// 
    /// # 返回值
    /// * `Result<Vec<Option<TransactionInfo>>>` - 交易信息列表
    pub async fn get_transactions_batch(
        &self,
        signatures: &[String],
        encoding: Option<&str>,
        commitment: Option<&str>,
        max_supported_transaction_version: Option<u8>
    ) -> Result<Vec<Option<TransactionInfo>>> {
        debug!("=== 批量获取交易信息 ===");
        debug!("交易签名数量: {}", signatures.len());

        let mut results = Vec::new();
        
        for signature in signatures {
            match self.get_transaction(signature, encoding, commitment, max_supported_transaction_version).await {
                Ok(transaction_info) => {
                    results.push(transaction_info);
                }
                Err(e) => {
                    debug!("获取交易 {} 失败: {}", signature, e);
                    results.push(None);
                }
            }
        }

        debug!("批量获取完成，成功获取 {} 个交易", results.iter().filter(|t| t.is_some()).count());
        Ok(results)
    }
}
