use anyhow::Result;
use log::{debug, info, warn};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use serde::{Deserialize, Serialize};

use crate::{
    client::http_client::HttpRpcClient,
    services::solana_rpc_service::SolanaRpcService,
    parsers::transaction_parser::TransactionParser,
    pool::token_pool_finder::PoolType,
    constants::programs,
};

/// 从CPI日志中提取的池子信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpiPoolInfo {
    /// 池子地址
    pub pool_address: Pubkey,
    /// 代币地址
    pub token_address: Pubkey,
    /// 交易签名
    pub signature: String,
    /// 提取时间戳
    pub extracted_at: u64,
    /// 池子类型
    pub pool_type: PoolType,
}



/// ExtendAccountEvent事件数据结构
/// 根据IDL定义: account(pubkey) + user(pubkey) + current_size(u64) + new_size(u64) + timestamp(i64)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, borsh::BorshSerialize, borsh::BorshDeserialize)]
pub struct ExtendAccountEvent {
    /// 账户地址
    pub account: Pubkey,
    /// 用户地址
    pub user: Pubkey,
    /// 当前大小
    pub current_size: u64,
    /// 新大小
    pub new_size: u64,
    /// 时间戳
    pub timestamp: i64,
}

/// CreateEvent事件结构体 (pump.fun代币创建事件)
#[derive(Debug, Clone, Serialize, Deserialize, borsh::BorshSerialize, borsh::BorshDeserialize)]
pub struct CreateEvent {
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub user: Pubkey,
    pub creator: Pubkey,
    pub timestamp: i64,
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub token_total_supply: u64,
}

/// BuyEvent事件数据结构
/// 根据IDL定义的字段顺序
#[derive(Debug, Clone, Serialize, Deserialize, borsh::BorshSerialize, borsh::BorshDeserialize)]
pub struct BuyEvent {
    pub timestamp: i64,
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_out: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_out_without_lp_fee: u64,
    pub user_quote_amount_out: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
}

/// SellEvent事件数据结构
/// 根据IDL定义的字段顺序
#[derive(Debug, Clone, Serialize, Deserialize, borsh::BorshSerialize, borsh::BorshDeserialize)]
pub struct SellEvent {
    pub timestamp: i64,
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_out: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_out_without_lp_fee: u64,
    pub user_quote_amount_out: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
}

/// CPI池子提取器
/// 专门负责从MINT事务的CPI日志中提取池子信息
pub struct CpiPoolExtractor {
    rpc_service: SolanaRpcService,
    transaction_parser: TransactionParser,
}

impl CpiPoolExtractor {
    /// 创建新的CPI池子提取器
    pub fn new(rpc_client: HttpRpcClient) -> Self {
        Self {
            rpc_service: SolanaRpcService::new(rpc_client),
            transaction_parser: TransactionParser::new(),
        }
    }

    /// 从指定交易签名中提取池子信息
    /// 
    /// # 参数
    /// * `signature` - 交易签名
    /// * `token_address` - 目标代币地址
    /// 
    /// # 返回值
    /// * `Result<Option<CpiPoolInfo>>` - 提取的池子信息
    pub async fn extract_pool_from_transaction(
        &self,
        signature: &str,
        token_address: &str,
    ) -> Result<Option<CpiPoolInfo>> {
        info!("🔍 开始从交易签名中提取池子信息");
        info!("  交易签名: {}", signature);
        info!("  代币地址: {}", token_address);

        // 1. 获取交易详情
        let transaction_info = match self.rpc_service.get_transaction(
            signature,
            Some("json"),
            Some("confirmed"),
            Some(0)
        ).await? {
            Some(tx) => tx,
            None => {
                warn!("❌ 无法获取交易信息: {}", signature);
                return Ok(None);
            }
        };

        info!("✅ 成功获取交易信息");

        // 2. 检查交易是否包含Pump.fun相关程序
        if !self.contains_pump_programs(&transaction_info) {
            warn!("❌ 交易不包含Pump.fun相关程序");
            return Ok(None);
        }

        info!("✅ 交易包含Pump.fun相关程序");

        // 3. 从CPI日志中提取池子地址
        let pools = self.transaction_parser.extract_pump_pools_from_transaction(
            &transaction_info,
            token_address
        )?;

        if pools.is_empty() {
            warn!("❌ 未从CPI日志中提取到池子信息");
            return Ok(None);
        }

        // 4. 转换为CpiPoolInfo
        let pool = &pools[0]; // 取第一个池子
        let token_pubkey = Pubkey::from_str(token_address)?;
        
        let cpi_pool_info = CpiPoolInfo {
            pool_address: pool.pool_address,
            token_address: token_pubkey,
            signature: signature.to_string(),
            extracted_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            pool_type: pool.pool_type.clone(),
        };

        info!("🎉 成功提取池子信息:");
        info!("  池子地址: {}", cpi_pool_info.pool_address);
        info!("  代币地址: {}", cpi_pool_info.token_address);
        info!("  池子类型: {:?}", cpi_pool_info.pool_type);

        Ok(Some(cpi_pool_info))
    }

    /// 检查交易是否包含Pump.fun相关程序
    fn contains_pump_programs(&self, transaction_info: &crate::pojo::solana::http::resp::TransactionInfo) -> bool {
        if let Some(transaction) = &transaction_info.transaction {
            let message = &transaction.message;
            let account_keys = &message.account_keys;
            for account_key in account_keys {
                if account_key == programs::PUMP_FUN_PROGRAM_ID ||
                   account_key == programs::PUMP_AMM_PROGRAM_ID {
                    return true;
                }
            }
        }
        false
    }

    /// 批量从多个交易中提取池子信息
    /// 
    /// # 参数
    /// * `signatures` - 交易签名列表
    /// * `token_address` - 目标代币地址
    /// 
    /// # 返回值
    /// * `Result<Vec<CpiPoolInfo>>` - 提取的池子信息列表
    pub async fn extract_pools_from_transactions(
        &self,
        signatures: &[String],
        token_address: &str,
    ) -> Result<Vec<CpiPoolInfo>> {
        info!("🔍 开始批量提取池子信息，交易数量: {}", signatures.len());
        
        let mut pools = Vec::new();
        
        for signature in signatures {
            match self.extract_pool_from_transaction(signature, token_address).await {
                Ok(Some(pool_info)) => {
                    pools.push(pool_info);
                    info!("✅ 从交易 {} 中提取到池子", signature);
                }
                Ok(None) => {
                    debug!("❌ 交易 {} 中未找到池子信息", signature);
                }
                Err(e) => {
                    warn!("⚠️ 处理交易 {} 时出错: {}", signature, e);
                }
            }
        }
        
        info!("🎉 批量提取完成，共找到 {} 个池子", pools.len());
        Ok(pools)
    }
}

/// 解析ExtendAccountEvent数据 (使用borsh反序列化)
pub fn parse_extend_account_event_data(cpi_data: &str) -> Result<ExtendAccountEvent> {
    // 1. 解码base58数据
    let decoded_data = bs58::decode(cpi_data).into_vec()
        .map_err(|e| anyhow::anyhow!("Base58解码失败: {}", e))?;

    // 2. 验证头部 - 跳过CPI标识符(8字节)和discriminator(8字节)
    if decoded_data.len() < 16 {
        return Err(anyhow::anyhow!("数据长度不足"));
    }

    // 3. 转换 - 使用borsh反序列化
    let event_data = &decoded_data[16..];
    let extend_event = borsh::from_slice::<ExtendAccountEvent>(event_data)
        .map_err(|e| anyhow::anyhow!("Borsh反序列化失败: {}", e))?;

    Ok(extend_event)
}

/// 解析CreateEvent数据 (使用borsh反序列化)
pub fn parse_create_event_data(cpi_data: &str) -> Result<CreateEvent> {
    // 1. 解码base58数据
    let decoded_data = bs58::decode(cpi_data).into_vec()
        .map_err(|e| anyhow::anyhow!("Base58解码失败: {}", e))?;

    // 2. 验证头部 - 跳过CPI标识符(8字节)和discriminator(8字节)
    if decoded_data.len() < 16 {
        return Err(anyhow::anyhow!("数据长度不足"));
    }

    // 3. 转换 - 使用borsh反序列化
    let event_data = &decoded_data[16..];
    let create_event = borsh::from_slice::<CreateEvent>(event_data)
        .map_err(|e| anyhow::anyhow!("Borsh反序列化失败: {}", e))?;

    Ok(create_event)
}

/// 解析BuyEvent数据 (使用borsh反序列化)
pub fn parse_buy_event_data(cpi_data: &str) -> Result<BuyEvent> {
    // 1. 解码base58数据
    let decoded_data = bs58::decode(cpi_data).into_vec()
        .map_err(|e| anyhow::anyhow!("Base58解码失败: {}", e))?;

    // 2. 验证头部 - 跳过CPI标识符(8字节)和discriminator(8字节)
    if decoded_data.len() < 16 {
        return Err(anyhow::anyhow!("数据长度不足"));
    }

    // 3. 转换 - 使用borsh反序列化
    let event_data = &decoded_data[16..];
    let buy_event = borsh::from_slice::<BuyEvent>(event_data)
        .map_err(|e| anyhow::anyhow!("Borsh反序列化失败: {}", e))?;

    Ok(buy_event)
}

/// 解析SellEvent数据 (使用borsh反序列化)
pub fn parse_sell_event_data(cpi_data: &str) -> Result<SellEvent> {
    // 1. 解码base58数据
    let decoded_data = bs58::decode(cpi_data).into_vec()
        .map_err(|e| anyhow::anyhow!("Base58解码失败: {}", e))?;

    // 2. 验证头部 - 跳过CPI标识符(8字节)和discriminator(8字节)
    if decoded_data.len() < 16 {
        return Err(anyhow::anyhow!("数据长度不足"));
    }

    // 3. 转换 - 使用borsh反序列化
    let event_data = &decoded_data[16..];
    let sell_event = borsh::from_slice::<SellEvent>(event_data)
        .map_err(|e| anyhow::anyhow!("Borsh反序列化失败: {}", e))?;

    Ok(sell_event)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use base64::{Engine as _, engine::general_purpose};
    use hex;
    use bs58;

    #[test]
    fn test_parse_extend_account_event() {
        // 初始化日志
        let _ = env_logger::try_init();

        println!("🔍 开始解析ExtendAccountEvent数据测试");

        // 您提供的base58编码的CPI数据
        let cpi_data = "21448NTnPMYUBkX8r9jEFxTjvLo4L3gUn5VnbKbnyVn73phiqxPmZ6XM7SdrfPCAK9KiSHKWy7KNfesG6cHYvGVqcvf65NCMjvgRvanSswDqvb75nqVtgQsggWjUkq7qbJA6vo1GUUDAhK5";

        // 解析ExtendAccountEvent
        let parsed_event = parse_extend_account_event_data(cpi_data).expect("解析ExtendAccountEvent失败");

        println!("✅ ExtendAccountEvent 解析成功:");
        println!("{:#?}", parsed_event);

        // 验证数据合理性
        assert!(parsed_event.new_size >= parsed_event.current_size, "新大小应该大于等于当前大小");
        assert_ne!(parsed_event.account, Pubkey::default(), "账户地址不应为默认值");
        assert_ne!(parsed_event.user, Pubkey::default(), "用户地址不应为默认值");
    }

    #[test]
    fn test_parse_create_event() {
        let _ = env_logger::try_init();

        println!("🔍 开始解析 CreateEvent 数据测试");

        // 您提供的base58编码的CPI数据
        let cpi_data = "n5Uk7MBThkrqPHmuvS44LPixN5JwA4hH5LKNskwZYJtjWRsKidHVSE72mtesfSp6PetB36b8UusHKygUeuWPqL7ayzA8UBbaFTVnUQxbDMMHv22Mds5Dys6AmXYfTPdVmhPptwKYagsxACiQE9u84FDX3pNcF1mnkBgsEVa6KJzUpQcsgCwXttb78otURfWCCJkTGbeLpSUeCnQj7s3KjfdtXS1H53B4y2NyAp2byVLwNPAyyykZtk4KdtefwNrvK5q3yhssz3hgCt3FgaWqBtz2xj1a12bQP9NCFpRFHZcNA6wsQgaZQJFDBAjs8ewBjErAuHQyYMD3JT8uKcUP8en3dDVjKLYyjhZV1kufDBSdb29ktgemPKzbGQLE3nEAFnRUyJry7h2MHbSDarTKbyy";

        // 解析CreateEvent
        let parsed_event = parse_create_event_data(cpi_data).expect("解析CreateEvent失败");

        println!("✅ CreateEvent 解析成功:");
        println!("{:#?}", parsed_event);

        // 验证数据合理性
        assert!(!parsed_event.name.is_empty(), "代币名称不应为空");
        assert!(!parsed_event.symbol.is_empty(), "代币符号不应为空");
        assert!(parsed_event.timestamp > 0, "时间戳应该大于0");
        assert!(parsed_event.token_total_supply > 0, "代币总供应量应该大于0");
    }
}
