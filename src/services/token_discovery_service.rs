use anyhow::{anyhow, Result};
use backoff::{future::retry, ExponentialBackoff};
use borsh::BorshDeserialize;
use futures::{sink::SinkExt, stream::StreamExt};
use log::{debug, error, info};
use serde_json::{json, Value};
use solana_sdk::signature::Signature;
use solana_transaction_status::UiTransactionEncoding;
use std::str::FromStr;
use std::{collections::HashMap, sync::Arc, time::Duration};
use tokio::sync::Mutex;
use tonic::transport::ClientTlsConfig;
use yellowstone_grpc_client::{GeyserGrpcClient, Interceptor};
use yellowstone_grpc_proto::{
    convert_from,
    geyser::SubscribeRequestFilterTransactions,
    prelude::{
        subscribe_update::UpdateOneof, CommitmentLevel, SubscribeRequest, SubscribeRequestPing,
        SubscribeUpdateTransactionInfo,
    },
};

use crate::{
    client::http_client::HttpRpcClient,
    config::GrpcConfig,
    constants::programs,
    db::pool_db::PoolDatabase,
    services::cpi_pool_extractor::{CpiPoolExtractor, CpiPoolInfo, CreateEvent},
    token_manager::TokenPoolManager,
    pool::token_pool_finder::{PoolDiscoveryResult, PoolSearchResult, PoolType},
};

type TransactionsFilterMap = HashMap<String, SubscribeRequestFilterTransactions>;

/// 发现的新代币信息
#[derive(Debug, Clone)]
pub struct DiscoveredToken {
    pub mint_address: String,
    pub signature: String,
    pub slot: u64,
    pub block_time: Option<i64>,
    /// 从CPI日志中提取的池子信息（应该总是存在）
    pub pool_info: Option<CpiPoolInfo>,
}

/// 代币和池子的完整发现结果
#[derive(Debug, Clone)]
pub struct TokenPoolDiscoveryResult {
    /// 代币地址
    pub token_address: String,
    /// 池子地址（bonding curve地址）
    pub pool_address: String,
    /// 交易签名
    pub signature: String,
    /// 区块槽位
    pub slot: u64,
    /// 区块时间
    pub block_time: Option<i64>,
    /// 代币创建事件的完整信息
    pub create_event: crate::services::cpi_pool_extractor::CreateEvent,
}

/// 代币发现服务
pub struct TokenDiscoveryService {
    config: GrpcConfig,
    token_manager: Arc<TokenPoolManager>,
    discovered_tokens: Arc<Mutex<Vec<DiscoveredToken>>>,
    cpi_extractor: CpiPoolExtractor,
    pool_db: Option<PoolDatabase>,
    // #debug-start#
    max_tokens: usize, // 调试模式下限制最大代币数量
                       // #debug-end#
}

impl TokenDiscoveryService {
    /// 创建新的代币发现服务
    pub fn new(
        config: GrpcConfig,
        token_manager: Arc<TokenPoolManager>,
        rpc_client: HttpRpcClient,
    ) -> Self {
        Self {
            config,
            token_manager,
            discovered_tokens: Arc::new(Mutex::new(Vec::new())),
            cpi_extractor: CpiPoolExtractor::new(rpc_client),
            pool_db: None,
            // #debug-start#
            max_tokens: 10, // 调试模式下限制为10个代币
                            // #debug-end#
        }
    }

    /// 创建带数据库的代币发现服务
    pub fn with_database(
        config: GrpcConfig,
        token_manager: Arc<TokenPoolManager>,
        rpc_client: HttpRpcClient,
        db_path: &str,
    ) -> Result<Self> {
        let pool_db = PoolDatabase::new(db_path)?;
        Ok(Self {
            config,
            token_manager,
            discovered_tokens: Arc::new(Mutex::new(Vec::new())),
            cpi_extractor: CpiPoolExtractor::new(rpc_client),
            pool_db: Some(pool_db),
            // #debug-start#
            max_tokens: 10, // 调试模式下限制为10个代币
                            // #debug-end#
        })
    }

    /// 启动代币发现服务
    pub async fn start_discovery(&self) -> Result<()> {
        info!("🔍 启动代币发现服务");

        // #debug-start#
        info!("⚠️  调试模式：最多发现 {} 个代币后停止", self.max_tokens);
        // #debug-end#

        let zero_attempts = Arc::new(Mutex::new(true));

        // 使用指数退避重试策略
        retry(ExponentialBackoff::default(), move || {
            let config = self.config.clone();
            let token_manager = Arc::clone(&self.token_manager);
            let discovered_tokens = Arc::clone(&self.discovered_tokens);
            let zero_attempts = Arc::clone(&zero_attempts);
            // #debug-start#
            let max_tokens = self.max_tokens;
            // #debug-end#

            async move {
                let mut zero_attempts = zero_attempts.lock().await;
                if *zero_attempts {
                    *zero_attempts = false;
                } else {
                    info!("重试连接到 gRPC 服务器");
                }
                drop(zero_attempts);

                let client = Self::connect_grpc(&config)
                    .await
                    .map_err(backoff::Error::transient)?;
                info!("✅ 成功连接到 gRPC 服务器");

                let request =
                    Self::create_token_discovery_request().map_err(backoff::Error::Permanent)?;

                Self::process_token_discovery_stream(
                    client,
                    request,
                    token_manager,
                    discovered_tokens,
                    &self.cpi_extractor,
                    &self.pool_db,
                    // #debug-start#
                    max_tokens,
                    // #debug-end#
                )
                .await
                .map_err(backoff::Error::transient)?;

                Ok::<(), backoff::Error<anyhow::Error>>(())
            }
        })
        .await
        .map_err(Into::into)
    }

    /// 连接到 gRPC 服务器
    async fn connect_grpc(config: &GrpcConfig) -> Result<GeyserGrpcClient<impl Interceptor>> {
        use std::env;

        info!("连接到 gRPC 服务器: {}", config.endpoint);

        // 设置代理环境变量（从配置获取）
        env::set_var("HTTP_PROXY", "http://192.168.96.1:7897");
        env::set_var("HTTPS_PROXY", "http://192.168.96.1:7897");
        info!("设置代理: http://192.168.96.1:7897");

        let mut builder = GeyserGrpcClient::build_from_shared(config.endpoint.clone())?;

        // 设置认证令牌（如果有）
        if let Some(token) = &config.x_token {
            if !token.is_empty() {
                builder = builder.x_token(Some(token.clone()))?;
                info!("使用认证令牌");
            }
        }

        let client = builder
            .connect_timeout(Duration::from_secs(config.timeout_seconds))
            .timeout(Duration::from_secs(config.timeout_seconds))
            .tls_config(ClientTlsConfig::new().with_native_roots())?
            .max_decoding_message_size(1024 * 1024 * 1024)
            .connect()
            .await?;

        Ok(client)
    }

    /// 创建代币发现订阅请求
    /// 参考examples/stream_pump_fun_new_minted_tokens的配置，只订阅token创建事件
    fn create_token_discovery_request() -> Result<SubscribeRequest> {
        let mut transactions: TransactionsFilterMap = HashMap::new();

        transactions.insert(
            "client".to_owned(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),   // 排除投票交易
                failed: Some(false), // 排除失败的交易
                signature: None,
                account_include: vec![programs::MINT_AUTHORITY.to_string()], // 只监听 Pump.fun  MINT_AUTHORITY 程序
                account_exclude: vec![],                                     // 不排除任何账户
                account_required: vec![],                                    // 不要求特定账户
            },
        );

        let request = SubscribeRequest {
            slots: HashMap::default(),
            accounts: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            entry: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: Vec::default(),
            ping: None,
            from_slot: None,
        };

        info!("=== 代币发现订阅请求（优化版）===");
        info!("监控程序ID: {}", programs::MINT_AUTHORITY);
        info!("排除投票交易: true");
        info!("排除失败交易: true");
        info!("确认级别: Processed");
        info!("订阅类型: 只订阅token创建事件");
        debug!("request: {:#?}", request);
        info!("=====================================");

        Ok(request)
    }

    /// 处理代币发现数据流
    async fn process_token_discovery_stream(
        mut client: GeyserGrpcClient<impl Interceptor>,
        request: SubscribeRequest,
        token_manager: Arc<TokenPoolManager>,
        discovered_tokens: Arc<Mutex<Vec<DiscoveredToken>>>,
        cpi_extractor: &CpiPoolExtractor,
        pool_db: &Option<PoolDatabase>,
        // #debug-start#
        max_tokens: usize,
        // #debug-end#
    ) -> Result<()> {
        info!("🎯 代币发现数据流已开启");

        let (mut subscribe_tx, mut stream) = client.subscribe_with_request(Some(request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => match msg.update_oneof {
                    Some(UpdateOneof::Transaction(msg)) => {
                        debug!("msg: {:?}", msg);
                        // 从交易信息中获取签名
                        let signature_str = if let Some(ref tx) = msg.transaction {
                            bs58::encode(&tx.signature).into_string()
                        } else {
                            "unknown".to_string()
                        };
                        debug!("收到交易消息 - 签名: {}, 槽位: {}", signature_str, msg.slot);
                        // #debug-start#
                        // 检查是否已达到最大代币数量
                        {
                            let tokens = discovered_tokens.lock().await;
                            if tokens.len() >= max_tokens {
                                info!("🛑 已发现 {} 个代币，达到调试限制，停止发现", max_tokens);
                                break;
                            }
                        }
                        // #debug-end#

                        if let Err(e) = Self::process_transaction_for_token_discovery(
                            msg,
                            Arc::clone(&token_manager),
                            Arc::clone(&discovered_tokens),
                            &cpi_extractor,
                            pool_db,
                        )
                        .await
                        {
                            error!("处理交易失败: {}", e);
                        }
                    }
                    Some(UpdateOneof::Ping(_)) => {
                        debug!("收到Ping，发送Pong响应");
                        subscribe_tx
                            .send(SubscribeRequest {
                                ping: Some(SubscribeRequestPing { id: 1 }),
                                ..Default::default()
                            })
                            .await?;
                    }
                    Some(UpdateOneof::Pong(_)) => {
                        debug!("收到Pong响应");
                    }
                    None => {
                        error!("消息中未找到更新内容");
                        break;
                    }
                    _ => {
                        debug!("收到其他类型的更新");
                    }
                },
                Err(error) => {
                    error!("gRPC 流错误: {error:?}");
                    break;
                }
            }
        }

        info!("🔚 代币发现数据流已关闭");
        Ok(())
    }

    /// 处理交易以发现新代币和对应的池子
    /// 正确流程：
    /// 1. 订阅代币创建事件
    /// 2. 解析日志获取代币token和池子信息
    /// 3. 放入数据库
    /// 4. 建立任务依次执行
    async fn process_transaction_for_token_discovery(
        msg: yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
        token_manager: Arc<TokenPoolManager>,
        discovered_tokens: Arc<Mutex<Vec<DiscoveredToken>>>,
        cpi_extractor: &CpiPoolExtractor,
        pool_db: &Option<PoolDatabase>,
    ) -> Result<()> {
        let tx = msg
            .transaction
            .as_ref()
            .ok_or(anyhow!("交易消息中没有交易数据"))?;

        let signature_str = if let Some(transaction) = &tx.transaction {
            hex::encode(&transaction.signatures[0])
        } else {
            "unknown".to_string()
        };
        info!("🔍 处理Pump.fun交易: {}", signature_str);

        // 步骤1: 直接从交易中解析代币创建事件和池子信息
        // 不依赖postTokenBalances，而是直接解析CPI日志
        match Self::extract_token_and_pool_from_transaction(tx, &msg, cpi_extractor).await {
            Ok(Some(discovery_result)) => {
                info!("🎉 成功发现代币和池子！");
                info!("  代币地址: {}", discovery_result.token_address);
                info!("  池子地址: {}", discovery_result.pool_address);
                info!("  交易签名: {}", discovery_result.signature);

                // 步骤2: 保存到数据库
                if let Some(ref db) = pool_db {
                    // 创建池子搜索结果
                    let pool_search_result = PoolSearchResult {
                        pool_address: solana_sdk::pubkey::Pubkey::from_str(&discovery_result.pool_address)?,
                        program_id: solana_sdk::pubkey::Pubkey::from_str(&programs::PUMP_FUN_PROGRAM_ID)?,
                        base_mint: solana_sdk::pubkey::Pubkey::from_str(&discovery_result.token_address)?,
                        quote_mint: solana_sdk::pubkey::Pubkey::from_str("So11111111111111111111111111111111111111112")?, // SOL
                        pool_type: PoolType::PumpFun,
                    };

                    // 创建池子发现结果
                    let pool_discovery_result = PoolDiscoveryResult {
                        token_address: discovery_result.token_address.clone(),
                        pools: vec![pool_search_result],
                        discovered_at: std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs(),
                    };

                    if let Err(e) = db.save_pool_discovery_result(&pool_discovery_result) {
                        error!("保存发现的池子到数据库失败: {}", e);
                    } else {
                        info!(
                            "💾 成功保存池子到数据库: token={}, pool={}",
                            discovery_result.token_address,
                            discovery_result.pool_address
                        );
                    }

                    // 创建发现的代币信息（用于内存列表）
                    let discovered_token = DiscoveredToken {
                        mint_address: discovery_result.token_address.clone(),
                        signature: discovery_result.signature.clone(),
                        slot: discovery_result.slot,
                        block_time: discovery_result.block_time,
                        pool_info: Some(CpiPoolInfo {
                            pool_address: solana_sdk::pubkey::Pubkey::from_str(&discovery_result.pool_address)?,
                            token_address: solana_sdk::pubkey::Pubkey::from_str(&discovery_result.token_address)?,
                            signature: discovery_result.signature.clone(),
                            extracted_at: std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap()
                                .as_secs(),
                            pool_type: PoolType::PumpFun,
                        }),
                    };

                    // 添加到发现列表
                    {
                        let mut tokens = discovered_tokens.lock().await;
                        tokens.push(discovered_token);
                        info!("📊 当前已发现 {} 个代币", tokens.len());
                    }
                }

                // 步骤3: 建立任务 - 将代币和池子信息添加到TokenPoolManager
                // 这里直接提供池子信息，避免后续再次查找
                let pool_result = crate::pool::token_pool_finder::PoolSearchResult {
                    pool_address: solana_sdk::pubkey::Pubkey::from_str(
                        &discovery_result.pool_address,
                    )?,
                    program_id: solana_sdk::pubkey::Pubkey::from_str(
                        crate::constants::programs::MINT_AUTHORITY,
                    )?,
                    base_mint: solana_sdk::pubkey::Pubkey::from_str(
                        &discovery_result.token_address,
                    )?,
                    quote_mint: solana_sdk::pubkey::Pubkey::from_str(
                        "So11111111111111111111111111111111111111112",
                    )?, // SOL
                    pool_type: crate::pool::token_pool_finder::PoolType::PumpFun,
                };

                // 直接使用已知的池子信息添加代币，避免重复的池子发现
                if let Err(e) = token_manager
                    .add_token_with_pools(discovery_result.token_address.clone(), Some(vec![pool_result]))
                    .await
                {
                    error!("添加代币和池子到管理器失败: {}", e);
                } else {
                    info!(
                        "✅ 成功添加代币和池子到管理器: {} -> {}",
                        discovery_result.token_address, discovery_result.pool_address
                    );
                }
            }
            Ok(None) => {
                debug!("交易中未发现代币创建事件");
            }
            Err(e) => {
                error!("解析交易失败: {}", e);
            }
        }

        Ok(())
    }

    /// 从交易中提取代币和池子信息
    /// 解析所有pump.fun事件类型，确保发现代币的同时找到池子
    async fn extract_token_and_pool_from_transaction(
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
        msg: &yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
        cpi_extractor: &CpiPoolExtractor,
    ) -> Result<Option<TokenPoolDiscoveryResult>> {
        debug!("🔍 从交易中提取代币和池子信息");

        // 检查交易是否包含Pump.fun相关程序
        if !Self::contains_pump_programs(tx) {
            debug!("❌ 交易不包含Pump.fun相关程序，跳过");
            return Ok(None);
        }

        // 检查交易日志中的 "Program data:" 条目来查找各种事件
        if let Some(meta) = &tx.meta {
            // 检查 inner_instructions 中的 CPI 数据（字节数组格式）
            for inner_instruction_group in &meta.inner_instructions {
                for inner_instruction in &inner_instruction_group.instructions {
                    // 检查是否为 pump.fun 程序的指令
                    if Self::is_pump_program_instruction(
                        tx,
                        inner_instruction.program_id_index as u8,
                    ) {
                        debug!(
                            "检查InnerInstruction CPI数据，长度: {}",
                            inner_instruction.data.len()
                        );

                        // 尝试解析字节数组格式的CPI事件
                        if let Some(result) =
                            Self::parse_cpi_data_bytes(&inner_instruction.data, tx, msg).await?
                        {
                            return Ok(Some(result));
                        }
                    }
                }
            }
        }

        debug!("❌ 未在交易中找到相关的pump.fun事件");
        Ok(None)
    }

    /// 检查交易是否包含Pump.fun相关程序
    fn contains_pump_programs(
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
    ) -> bool {
        if let Some(transaction) = &tx.transaction {
            if let Some(message) = &transaction.message {
                let account_keys = &message.account_keys;

                for account_key in account_keys {
                    if account_key.len() == 32 {
                        let mut pubkey_bytes = [0u8; 32];
                        pubkey_bytes.copy_from_slice(account_key);
                        let account_pubkey =
                            solana_sdk::pubkey::Pubkey::new_from_array(pubkey_bytes);
                        let account_str = account_pubkey.to_string();

                        // 检查是否包含任何pump.fun相关程序
                        if account_str == crate::constants::programs::PUMP_FUN_PROGRAM_ID
                            || account_str == crate::constants::programs::PUMP_AMM_PROGRAM_ID
                        {
                            debug!("✅ 交易包含Pump.fun程序: {}", account_str);
                            return true;
                        }
                    }
                }
            }
        }
        false
    }

    /// 检查指定的程序索引是否为pump.fun相关程序
    fn is_pump_program_instruction(
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
        program_id_index: u8,
    ) -> bool {
        if let Some(transaction) = &tx.transaction {
            if let Some(message) = &transaction.message {
                let account_keys = &message.account_keys;

                if let Some(account_key) = account_keys.get(program_id_index as usize) {
                    if account_key.len() == 32 {
                        let mut pubkey_bytes = [0u8; 32];
                        pubkey_bytes.copy_from_slice(account_key);
                        let account_pubkey =
                            solana_sdk::pubkey::Pubkey::new_from_array(pubkey_bytes);
                        let account_str = account_pubkey.to_string();

                        // 检查是否为pump.fun相关程序
                        return account_str == crate::constants::programs::PUMP_FUN_PROGRAM_ID
                            || account_str == crate::constants::programs::PUMP_AMM_PROGRAM_ID
                            || account_str == crate::constants::programs::MINT_AUTHORITY;
                    }
                }
            }
        }
        false
    }

    /// 解析字节数组格式的CPI数据
    async fn parse_cpi_data_bytes(
        data: &[u8],
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
        msg: &yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
    ) -> Result<Option<TokenPoolDiscoveryResult>> {
        debug!("解析字节数组CPI数据，长度: {}", data.len());
        debug!("数据前16字节: {:?}", &data[..16.min(data.len())]);

        // 检查是否为CPI日志
        let is_cpi = crate::parsers::pump_amm_events::EventParser::is_cpi_log(data);
        debug!("是否为CPI日志: {}", is_cpi);

        if !is_cpi {
            debug!("不是CPI日志格式");
            return Ok(None);
        }

        // 检查数据长度是否足够包含discriminator
        if data.len() < 16 {
            debug!("CPI数据长度不足");
            return Ok(None);
        }

        // 提取discriminator（跳过CPI头部8字节）
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let mut discriminator = [0u8; 8];
        discriminator.copy_from_slice(&data[discriminator_start..discriminator_end]);

        debug!("CPI事件discriminator: {:?}", discriminator);

        let signature_str = if let Some(transaction) = &tx.transaction {
            hex::encode(&transaction.signatures[0])
        } else {
            "unknown".to_string()
        };

        // 根据discriminator类型解析不同的事件
        match discriminator {
            crate::constants::events::CREATE_EVENT_EVENT_DISCM => {
                info!("🎉 发现CreateEvent（pump.fun代币创建事件）！");
                if let Ok(create_event) = crate::pojo::solana::pump_events::PumpEventParser::parse_create_event(data) {
                    info!("  代币名称: {}", create_event.name);
                    info!("  代币符号: {}", create_event.symbol);
                    info!("  代币地址: {}", create_event.mint);
                    info!("  池子地址: {}", create_event.bonding_curve);

                    return Ok(Some(Self::create_discovery_result_from_create_event(
                        create_event,
                        signature_str,
                        msg.slot,
                    )));
                }
            }
            crate::constants::events::TRADE_EVENT_EVENT_DISCM => {
                info!("🎉 发现TradeEvent（pump.fun交易事件）！");
                if let Ok(trade_event) = crate::pojo::solana::pump_events::PumpEventParser::parse_trade_event(data) {
                    info!("  代币地址: {}", trade_event.mint);
                    info!("  用户地址: {}", trade_event.user);
                    info!("  交易类型: {}", if trade_event.is_buy { "买入" } else { "卖出" });
                    info!("  SOL数量: {}", trade_event.sol_amount);
                    info!("  代币数量: {}", trade_event.token_amount);

                    return Ok(Some(Self::create_discovery_result_from_trade_event_v2(
                        trade_event,
                        signature_str,
                        msg.slot,
                    )));
                }
            }
            crate::constants::events::CREATE_POOL_EVENT => {
                info!("🎉 发现CreatePoolEvent（AMM池子创建）！");
                if let Ok(pool_event) = Self::parse_create_pool_event(data) {
                    info!("  基础代币: {}", pool_event.base_mint);
                    info!("  报价代币: {}", pool_event.quote_mint);
                    info!("  池子地址: {}", pool_event.pool);

                    return Ok(Some(Self::create_discovery_result_from_pool_event(
                        pool_event,
                        signature_str,
                        msg.slot,
                    )));
                }
            }
            crate::constants::events::BUY_EVENT => {
                info!("🎉 发现BuyEvent（购买事件）！");
                if let Ok(buy_event) = Self::parse_buy_event(data) {
                    info!("  池子地址: {}", buy_event.pool);
                    info!("  用户地址: {}", buy_event.user);
                    info!("  购买数量: {}", buy_event.base_amount_out);

                    return Ok(Some(Self::create_discovery_result_from_trade_event(
                        buy_event.pool,
                        signature_str,
                        msg.slot,
                        "BuyEvent",
                    )));
                }
            }
            crate::constants::events::SELL_EVENT => {
                info!("🎉 发现SellEvent（出售事件）！");
                if let Ok(sell_event) =
                    crate::parsers::pump_amm_events::EventParser::deserialize_sell_event(data)
                {
                    info!("  池子地址: {}", sell_event.pool);
                    info!("  用户地址: {}", sell_event.user);
                    info!("  出售数量: {}", sell_event.base_amount_in);

                    return Ok(Some(Self::create_discovery_result_from_trade_event(
                        sell_event.pool,
                        signature_str,
                        msg.slot,
                        "SellEvent",
                    )));
                }
            }
            _ => {
                debug!("未识别的CPI事件类型，discriminator: {:?}", discriminator);
            }
        }

        Ok(None)
    }

    /// 解析pump.fun事件以发现代币和池子
    /// 支持多种事件类型：CreateEvent, CreatePoolEvent, BuyEvent, SellEvent等
    async fn parse_pump_event_for_token_discovery(
        data_part: &str,
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
        msg: &yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
    ) -> Result<Option<TokenPoolDiscoveryResult>> {
        // 1. 首先尝试解析原始pump.fun的CreateEvent（代币创建事件）
        if let Ok(create_event) =
            crate::services::cpi_pool_extractor::parse_create_event_data(data_part)
        {
            info!("🎉 成功解析CreateEvent（代币创建）！");
            info!("  代币名称: {}", create_event.name);
            info!("  代币符号: {}", create_event.symbol);
            info!("  代币地址: {}", create_event.mint);
            info!("  池子地址: {}", create_event.bonding_curve);

            let signature_str = if let Some(transaction) = &tx.transaction {
                hex::encode(&transaction.signatures[0])
            } else {
                "unknown".to_string()
            };

            return Ok(Some(TokenPoolDiscoveryResult {
                token_address: create_event.mint.to_string(),
                pool_address: create_event.bonding_curve.to_string(),
                signature: signature_str,
                slot: msg.slot,
                block_time: None,
                create_event,
            }));
        }

        // 2. 尝试解析AMM事件（CreatePoolEvent, BuyEvent, SellEvent等）
        if let Some(result) = Self::parse_amm_events_for_token_discovery(data_part, tx, msg).await?
        {
            return Ok(Some(result));
        }

        Ok(None)
    }

    /// 解析AMM事件以发现代币和池子
    /// 处理CreatePoolEvent, BuyEvent, SellEvent等AMM相关事件
    async fn parse_amm_events_for_token_discovery(
        data_part: &str,
        tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransactionInfo,
        msg: &yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
    ) -> Result<Option<TokenPoolDiscoveryResult>> {
        // 解码base58数据
        let decoded_data = match bs58::decode(data_part).into_vec() {
            Ok(data) => data,
            Err(_) => {
                debug!("Base58解码失败");
                return Ok(None);
            }
        };

        // 检查是否为CPI日志
        if !crate::parsers::pump_amm_events::EventParser::is_cpi_log(&decoded_data) {
            debug!("不是CPI日志");
            return Ok(None);
        }

        // 检查数据长度是否足够包含discriminator
        if decoded_data.len() < 16 {
            debug!("数据长度不足");
            return Ok(None);
        }

        // 提取discriminator（跳过CPI头部8字节）
        let discriminator_start = 8;
        let discriminator_end = discriminator_start + 8;
        let mut discriminator = [0u8; 8];
        discriminator.copy_from_slice(&decoded_data[discriminator_start..discriminator_end]);

        let signature_str = if let Some(transaction) = &tx.transaction {
            hex::encode(&transaction.signatures[0])
        } else {
            "unknown".to_string()
        };

        // 根据discriminator类型解析不同的事件
        match discriminator {
            crate::constants::events::CREATE_POOL_EVENT => {
                info!("🎉 发现CreatePoolEvent（AMM池子创建）！");
                if let Ok(pool_event) = Self::parse_create_pool_event(&decoded_data) {
                    info!("  基础代币: {}", pool_event.base_mint);
                    info!("  报价代币: {}", pool_event.quote_mint);
                    info!("  池子地址: {}", pool_event.pool);

                    // 从CreatePoolEvent中提取代币和池子信息
                    return Ok(Some(Self::create_discovery_result_from_pool_event(
                        pool_event,
                        signature_str,
                        msg.slot,
                    )));
                }
            }
            crate::constants::events::BUY_EVENT => {
                info!("🎉 发现BuyEvent（购买事件）！");
                if let Ok(buy_event) = Self::parse_buy_event(&decoded_data) {
                    info!("  池子地址: {}", buy_event.pool);
                    info!("  用户地址: {}", buy_event.user);
                    info!("  购买数量: {}", buy_event.base_amount_out);

                    // 从BuyEvent中提取池子信息，但需要额外获取代币信息
                    return Ok(Some(Self::create_discovery_result_from_trade_event(
                        buy_event.pool,
                        signature_str,
                        msg.slot,
                        "BuyEvent",
                    )));
                }
            }
            crate::constants::events::SELL_EVENT => {
                info!("🎉 发现SellEvent（出售事件）！");
                if let Ok(sell_event) =
                    crate::parsers::pump_amm_events::EventParser::deserialize_sell_event(
                        &decoded_data,
                    )
                {
                    info!("  池子地址: {}", sell_event.pool);
                    info!("  用户地址: {}", sell_event.user);
                    info!("  出售数量: {}", sell_event.base_amount_in);

                    // 从SellEvent中提取池子信息
                    return Ok(Some(Self::create_discovery_result_from_trade_event(
                        sell_event.pool,
                        signature_str,
                        msg.slot,
                        "SellEvent",
                    )));
                }
            }
            _ => {
                debug!("未识别的事件类型，discriminator: {:?}", discriminator);
            }
        }

        Ok(None)
    }

    /// 解析CreatePoolEvent
    fn parse_create_pool_event(
        data: &[u8],
    ) -> Result<crate::parsers::pump_amm_events::CreatePoolEvent> {
        if data.len() < 16 {
            return Err(anyhow::anyhow!("数据长度不足"));
        }

        // 跳过CPI头部（8字节）和discriminator（8字节）
        let event_data = &data[16..];
        let event = crate::parsers::pump_amm_events::CreatePoolEvent::try_from_slice(event_data)?;
        Ok(event)
    }

    /// 解析BuyEvent
    fn parse_buy_event(data: &[u8]) -> Result<crate::parsers::pump_amm_events::BuyEvent> {
        if data.len() < 16 {
            return Err(anyhow::anyhow!("数据长度不足"));
        }

        // 跳过CPI头部（8字节）和discriminator（8字节）
        let event_data = &data[16..];
        let event = crate::parsers::pump_amm_events::BuyEvent::try_from_slice(event_data)?;
        Ok(event)
    }

    /// 从CreatePoolEvent创建发现结果
    fn create_discovery_result_from_pool_event(
        pool_event: crate::parsers::pump_amm_events::CreatePoolEvent,
        signature: String,
        slot: u64,
    ) -> TokenPoolDiscoveryResult {
        // 创建一个虚拟的CreateEvent用于兼容现有结构
        let create_event = CreateEvent {
            name: format!("Token_{}", pool_event.base_mint),
            symbol: format!("TKN_{}", &pool_event.base_mint.to_string()[..4]),
            uri: String::new(),
            mint: pool_event.base_mint,
            bonding_curve: pool_event.pool,
            user: pool_event.creator,
            creator: pool_event.creator,
            timestamp: pool_event.timestamp,
            virtual_token_reserves: pool_event.pool_base_amount,
            virtual_sol_reserves: pool_event.pool_quote_amount,
            real_token_reserves: pool_event.pool_base_amount,
            token_total_supply: pool_event.initial_liquidity,
        };

        TokenPoolDiscoveryResult {
            token_address: pool_event.base_mint.to_string(),
            pool_address: pool_event.pool.to_string(),
            signature,
            slot,
            block_time: None,
            create_event,
        }
    }

    /// 从交易事件创建发现结果（BuyEvent/SellEvent）
    fn create_discovery_result_from_trade_event(
        pool_address: solana_sdk::pubkey::Pubkey,
        signature: String,
        slot: u64,
        event_type: &str,
    ) -> TokenPoolDiscoveryResult {
        // 对于交易事件，我们只有池子地址，需要从池子信息推断代币地址
        // 这里创建一个占位符的CreateEvent
        let create_event = CreateEvent {
            name: format!("Unknown_Token_{}", event_type),
            symbol: "UNK".to_string(),
            uri: String::new(),
            mint: solana_sdk::pubkey::Pubkey::default(), // 需要后续查询获取
            bonding_curve: pool_address,
            user: solana_sdk::pubkey::Pubkey::default(),
            creator: solana_sdk::pubkey::Pubkey::default(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64,
            virtual_token_reserves: 0,
            virtual_sol_reserves: 0,
            real_token_reserves: 0,
            token_total_supply: 0,
        };

        TokenPoolDiscoveryResult {
            token_address: solana_sdk::pubkey::Pubkey::default().to_string(), // 需要后续查询
            pool_address: pool_address.to_string(),
            signature,
            slot,
            block_time: None,
            create_event,
        }
    }

    /// 从pump.fun CreateEvent创建发现结果
    fn create_discovery_result_from_create_event(
        create_event: crate::pojo::solana::pump_events::CreateEvent,
        signature: String,
        slot: u64,
    ) -> TokenPoolDiscoveryResult {
        // 直接使用pump.fun的CreateEvent数据
        let legacy_create_event = CreateEvent {
            name: create_event.name.clone(),
            symbol: create_event.symbol.clone(),
            uri: create_event.uri.clone(),
            mint: create_event.mint,
            bonding_curve: create_event.bonding_curve,
            user: create_event.user,
            creator: create_event.creator,
            timestamp: create_event.timestamp,
            virtual_token_reserves: create_event.virtual_token_reserves,
            virtual_sol_reserves: create_event.virtual_sol_reserves,
            real_token_reserves: create_event.real_token_reserves,
            token_total_supply: create_event.token_total_supply,
        };

        TokenPoolDiscoveryResult {
            token_address: create_event.mint.to_string(),
            pool_address: create_event.bonding_curve.to_string(),
            signature,
            slot,
            block_time: None,
            create_event: legacy_create_event,
        }
    }

    /// 从pump.fun TradeEvent创建发现结果
    fn create_discovery_result_from_trade_event_v2(
        trade_event: crate::pojo::solana::pump_events::TradeEvent,
        signature: String,
        slot: u64,
    ) -> TokenPoolDiscoveryResult {
        // 从TradeEvent创建一个CreateEvent用于兼容现有结构
        let create_event = CreateEvent {
            name: format!("Token_from_Trade_{}", trade_event.mint),
            symbol: format!("TRD_{}", &trade_event.mint.to_string()[..4]),
            uri: String::new(),
            mint: trade_event.mint,
            bonding_curve: solana_sdk::pubkey::Pubkey::default(), // TradeEvent中没有池子地址
            user: trade_event.user,
            creator: trade_event.creator,
            timestamp: trade_event.timestamp,
            virtual_token_reserves: trade_event.virtual_token_reserves,
            virtual_sol_reserves: trade_event.virtual_sol_reserves,
            real_token_reserves: trade_event.real_token_reserves,
            token_total_supply: 0, // TradeEvent中没有总供应量信息
        };

        TokenPoolDiscoveryResult {
            token_address: trade_event.mint.to_string(),
            pool_address: solana_sdk::pubkey::Pubkey::default().to_string(), // 需要后续查询
            signature,
            slot,
            block_time: None,
            create_event,
        }
    }

    /// 创建格式化的交易数据
    fn create_pretty_transaction(tx: SubscribeUpdateTransactionInfo) -> Result<Value> {
        Ok(json!({
            "signature": Signature::try_from(tx.signature.as_slice())?.to_string(),
            "isVote": tx.is_vote,
            "tx": convert_from::create_tx_with_meta(tx)
                .map_err(|error| anyhow!(error))?
                .encode(UiTransactionEncoding::Base64, Some(u8::MAX), true)?,
        }))
    }

    /// 获取已发现的代币列表
    pub async fn get_discovered_tokens(&self) -> Vec<DiscoveredToken> {
        let tokens = self.discovered_tokens.lock().await;
        tokens.clone()
    }

    /// 获取已发现的代币数量
    pub async fn get_discovered_count(&self) -> usize {
        let tokens = self.discovered_tokens.lock().await;
        tokens.len()
    }

    /// 清空已发现的代币列表
    pub async fn clear_discovered_tokens(&self) {
        let mut tokens = self.discovered_tokens.lock().await;
        tokens.clear();
        info!("清空已发现的代币列表");
    }
}
