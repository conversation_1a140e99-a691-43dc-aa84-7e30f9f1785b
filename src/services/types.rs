// 核心数据类型定义
//
// 定义了整个系统中使用的核心数据结构

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

/// 池子类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PoolType {
    /// PumpFun AMM池子
    PumpAMM,
    /// Raydium池子
    Raydium,
    /// Orca池子
    Orca,
    /// 其他类型的池子
    Other,
}

impl std::fmt::Display for PoolType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PoolType::PumpAMM => write!(f, "PumpAMM"),
            PoolType::Raydium => write!(f, "Raydium"),
            PoolType::Orca => write!(f, "Orca"),
            PoolType::Other => write!(f, "Other"),
        }
    }
}

/// 池子信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PoolInfo {
    /// 池子地址
    pub pool_address: Pubkey,
    /// 程序ID
    pub program_id: Pubkey,
    /// 基础代币地址
    pub base_mint: Pubkey,
    /// 报价代币地址
    pub quote_mint: Pubkey,
    /// 池子类型
    pub pool_type: PoolType,
}

/// 代币信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    /// 代币地址
    pub mint: Pubkey,
    /// 代币名称
    pub name: Option<String>,
    /// 代币符号
    pub symbol: Option<String>,
    /// 小数位数
    pub decimals: u8,
    /// 总供应量
    pub supply: Option<u64>,
}

/// 价格结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceResult {
    /// 当前价格
    pub current_price: f64,
    /// 价格变化信息
    pub price_change: Option<PriceChange>,
    /// 基础代币余额
    pub base_token_balance: u64,
    /// 报价代币余额
    pub quote_token_balance: u64,
    /// 计算时间戳
    pub timestamp: u64,
}

/// 价格变化信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceChange {
    /// 变化方向
    pub direction: PriceDirection,
    /// 绝对变化量
    pub absolute_change: f64,
    /// 百分比变化
    pub percentage_change: f64,
    /// 上一次价格
    pub previous_price: f64,
}

/// 价格变化方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PriceDirection {
    /// 价格上涨
    Up,
    /// 价格下跌
    Down,
    /// 价格无变化
    Stable,
}

impl std::fmt::Display for PriceDirection {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PriceDirection::Up => write!(f, "↗️"),
            PriceDirection::Down => write!(f, "↘️"),
            PriceDirection::Stable => write!(f, "➡️"),
        }
    }
}

/// 池子统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    /// 池子地址
    pub pool_address: String,
    /// 当前价格
    pub current_price: f64,
    /// 基础代币余额
    pub base_token_balance: u64,
    /// 报价代币余额
    pub quote_token_balance: u64,
    /// 总流动性（以USD计算）
    pub total_liquidity: f64,
    /// 24小时交易量
    pub volume_24h: Option<f64>,
    /// 24小时价格变化
    pub price_change_24h: Option<PriceChange>,
    /// 最后更新时间
    pub last_updated: u64,
}

/// 市场数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    /// 代币地址
    pub token_address: String,
    /// 当前价格（USD）
    pub price_usd: f64,
    /// 市值
    pub market_cap: Option<f64>,
    /// 24小时交易量
    pub volume_24h: f64,
    /// 24小时价格变化百分比
    pub price_change_24h_percent: f64,
    /// 流通供应量
    pub circulating_supply: Option<u64>,
    /// 总供应量
    pub total_supply: Option<u64>,
    /// 最后更新时间
    pub last_updated: u64,
}

/// 交易信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionInfo {
    /// 交易签名
    pub signature: String,
    /// 交易类型
    pub transaction_type: TransactionType,
    /// 涉及的代币数量
    pub token_amount: u64,
    /// 交易价格
    pub price: f64,
    /// 交易时间戳
    pub timestamp: u64,
    /// 交易者地址
    pub trader: Option<String>,
}

/// 交易类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TransactionType {
    /// 买入
    Buy,
    /// 卖出
    Sell,
    /// 添加流动性
    AddLiquidity,
    /// 移除流动性
    RemoveLiquidity,
    /// 交换
    Swap,
}

impl std::fmt::Display for TransactionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TransactionType::Buy => write!(f, "买入"),
            TransactionType::Sell => write!(f, "卖出"),
            TransactionType::AddLiquidity => write!(f, "添加流动性"),
            TransactionType::RemoveLiquidity => write!(f, "移除流动性"),
            TransactionType::Swap => write!(f, "交换"),
        }
    }
}

/// 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceError {
    /// 网络连接错误
    NetworkError(String),
    /// 数据解析错误
    ParseError(String),
    /// 配置错误
    ConfigError(String),
    /// 池子不存在
    PoolNotFound(String),
    /// 代币不存在
    TokenNotFound(String),
    /// 超时错误
    TimeoutError(String),
    /// 其他错误
    Other(String),
}

impl std::fmt::Display for ServiceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ServiceError::NetworkError(msg) => write!(f, "网络错误: {}", msg),
            ServiceError::ParseError(msg) => write!(f, "解析错误: {}", msg),
            ServiceError::ConfigError(msg) => write!(f, "配置错误: {}", msg),
            ServiceError::PoolNotFound(addr) => write!(f, "池子不存在: {}", addr),
            ServiceError::TokenNotFound(addr) => write!(f, "代币不存在: {}", addr),
            ServiceError::TimeoutError(msg) => write!(f, "超时错误: {}", msg),
            ServiceError::Other(msg) => write!(f, "其他错误: {}", msg),
        }
    }
}

impl std::error::Error for ServiceError {}
