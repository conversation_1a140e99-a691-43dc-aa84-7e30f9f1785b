use anyhow::Result;
use serde_json::{json, Value};
use std::fs;

/// 智能的sled数据库导出工具
/// 可以列出数据库中的所有树，并导出指定的树
fn main() -> Result<()> {
    let args: Vec<String> = std::env::args().collect();

    if args.len() < 2 {
        println!("用法:");
        println!("  {} <数据库路径> list                    # 列出数据库中的所有树", args[0]);
        println!("  {} <数据库路径> export [树名] [输出文件] # 导出指定树的数据", args[0]);
        println!("  {} <数据库路径> export-all [输出目录]   # 导出所有树的数据", args[0]);
        println!("  {} <数据库路径> delete [树名]           # 删除指定的树", args[0]);
        println!("  {} <数据库路径> clear [树名]            # 清空指定树的所有数据", args[0]);
        println!();
        println!("示例:");
        println!("  {} db/pool_cache.db list", args[0]);
        println!("  {} db/pool_cache.db export __sled__default pools.json", args[0]);
        println!("  {} db/pool_cache.db export tokens tokens.json", args[0]);
        println!("  {} db/pool_cache.db export-all exports/", args[0]);
        println!("  {} db/pool_cache.db delete \"pool_cache.json\"", args[0]);
        println!("  {} db/pool_cache.db clear \"__sled__default\"", args[0]);
        return Ok(());
    }

    let db_path = &args[1];
    let command = args.get(2).map(|s| s.as_str()).unwrap_or("list");

    println!("正在打开数据库: {}", db_path);

    // 打开数据库
    let db = sled::open(db_path)?;

    match command {
        "list" => list_trees(&db, db_path),
        "export" => {
            let tree_name = args.get(3);
            let output_file = args.get(4);
            export_tree(&db, db_path, tree_name, output_file)
        },
        "export-all" => {
            let output_dir = args.get(3).map(|s| s.as_str()).unwrap_or("exports");
            export_all_trees(&db, db_path, output_dir)
        },
        "delete" => {
            let tree_name = args.get(3);
            delete_tree(&db, tree_name)
        },
        "clear" => {
            let tree_name = args.get(3);
            clear_tree(&db, tree_name)
        },
        _ => {
            println!("❌ 未知命令: {}", command);
            println!("支持的命令: list, export, export-all, delete, clear");
            Ok(())
        }
    }
}

/// 列出数据库中的所有树
fn list_trees(db: &sled::Db, db_path: &str) -> Result<()> {
    println!("\n=== 数据库信息 ===");
    println!("数据库路径: {}", db_path);
    println!("数据库大小: {} 字节", db.size_on_disk()?);

    println!("\n=== 数据库中的树列表 ===");

    // 获取所有树名
    let tree_names = db.tree_names();

    if tree_names.is_empty() {
        println!("❌ 数据库中没有找到任何树");
        return Ok(());
    }

    println!("找到 {} 个树:", tree_names.len());

    for (i, tree_name) in tree_names.iter().enumerate() {
        let tree_name_str = String::from_utf8_lossy(tree_name);
        let tree = db.open_tree(tree_name)?;
        let record_count = tree.len();
        // 注意：sled的Tree没有size_on_disk方法，我们跳过这个统计
        // let tree_size = tree.size_on_disk()?;

        println!("{}. 树名: \"{}\"", i + 1, tree_name_str);
        println!("   记录数: {}", record_count);

        // 显示一些示例键（最多3个）
        let mut sample_keys = Vec::new();
        for result in tree.iter().take(3) {
            if let Ok((key, _)) = result {
                let key_str = String::from_utf8_lossy(&key);
                sample_keys.push(key_str.to_string());
            }
        }

        if !sample_keys.is_empty() {
            println!("   示例键: {:?}", sample_keys);
        }
        println!();
    }

    println!("💡 使用以下命令导出特定树:");
    for tree_name in &tree_names {
        let tree_name_str = String::from_utf8_lossy(tree_name);
        let safe_name = tree_name_str.replace("__sled__", "").replace("default", "main");
        let safe_name = if safe_name.is_empty() { "main" } else { &safe_name };
        println!("   cargo run --bin db_export -- {} export \"{}\" {}.json",
                 db_path, tree_name_str, safe_name);
    }

    Ok(())
}

/// 导出指定的树
fn export_tree(db: &sled::Db, db_path: &str, tree_name: Option<&String>, output_file: Option<&String>) -> Result<()> {
    let tree_name = match tree_name {
        Some(name) => name,
        None => {
            println!("❌ 请指定要导出的树名");
            println!("使用 'list' 命令查看可用的树");
            return Ok(());
        }
    };

    let output_path = match output_file {
        Some(file) => file.clone(),
        None => {
            let safe_name = tree_name.replace("__sled__", "").replace("default", "main");
            let safe_name = if safe_name.is_empty() { "main" } else { &safe_name };
            format!("{}.json", safe_name)
        }
    };

    // 检查树是否存在
    let tree_names = db.tree_names();
    let tree_name_bytes = tree_name.as_bytes();

    if !tree_names.iter().any(|name| name == tree_name_bytes) {
        println!("❌ 树 \"{}\" 不存在", tree_name);
        println!("使用 'list' 命令查看可用的树");
        return Ok(());
    }

    println!("正在导出树: \"{}\"", tree_name);
    println!("输出文件: {}", output_path);

    // 打开指定的树
    let tree = match db.open_tree(tree_name.as_bytes()) {
        Ok(tree) => tree,
        Err(e) => {
            println!("❌ 无法打开树 \"{}\": {}", tree_name, e);
            return Ok(());
        }
    };

    let mut data = Vec::new();
    let mut count = 0;

    // 遍历树中的所有键值对
    for result in tree.iter() {
        match result {
            Ok((key, value)) => {
                let key_str = String::from_utf8_lossy(&key).to_string();

                // 尝试解析值为JSON，如果失败则作为原始数据
                let value_json = match serde_json::from_slice::<Value>(&value) {
                    Ok(json_val) => json_val,
                    Err(_) => {
                        // 如果不是JSON，尝试作为字符串
                        match String::from_utf8(value.to_vec()) {
                            Ok(string_val) => Value::String(string_val),
                            Err(_) => {
                                // 如果不是UTF-8字符串，转换为hex
                                Value::String(format!("hex:{}", hex::encode(&value)))
                            }
                        }
                    }
                };

                data.push(json!({
                    "key": key_str,
                    "value": value_json
                }));

                count += 1;
            }
            Err(e) => {
                eprintln!("读取键值对失败: {}", e);
            }
        }
    }

    // 创建最终的JSON结构
    let export_data = json!({
        "database_path": db_path,
        "tree_name": tree_name,
        "total_records": count,
        "data": data
    });

    // 写入文件
    let json_string = serde_json::to_string_pretty(&export_data)?;
    fs::write(&output_path, json_string)?;

    println!("✅ 导出完成!");
    println!("   树名: \"{}\"", tree_name);
    println!("   总记录数: {}", count);
    println!("   输出文件: {}", output_path);

    // 显示树统计信息
    println!("\n=== 树统计信息 ===");
    println!("记录数量: {}", tree.len());

    Ok(())
}

/// 导出所有树
fn export_all_trees(db: &sled::Db, db_path: &str, output_dir: &str) -> Result<()> {
    println!("正在导出所有树到目录: {}", output_dir);

    // 创建输出目录
    fs::create_dir_all(output_dir)?;

    // 获取所有树名
    let tree_names = db.tree_names();

    if tree_names.is_empty() {
        println!("❌ 数据库中没有找到任何树");
        return Ok(());
    }

    println!("找到 {} 个树，开始导出...", tree_names.len());

    for (i, tree_name) in tree_names.iter().enumerate() {
        let tree_name_str = String::from_utf8_lossy(tree_name);
        let safe_name = tree_name_str.replace("__sled__", "").replace("default", "main");
        let safe_name = if safe_name.is_empty() { "main" } else { &safe_name };

        let output_file = format!("{}/{}.json", output_dir, safe_name);

        println!("\n[{}/{}] 导出树: \"{}\"", i + 1, tree_names.len(), tree_name_str);

        // 使用现有的export_tree函数
        let tree_name_string = tree_name_str.to_string();
        let output_file_string = output_file.clone();
        export_tree(db, db_path, Some(&tree_name_string), Some(&output_file_string))?;
    }

    println!("\n🎉 所有树导出完成!");
    println!("输出目录: {}", output_dir);

    Ok(())
}

/// 删除指定的树
fn delete_tree(db: &sled::Db, tree_name: Option<&String>) -> Result<()> {
    let tree_name = match tree_name {
        Some(name) => name,
        None => {
            println!("❌ 请指定要删除的树名");
            println!("使用 'list' 命令查看可用的树");
            return Ok(());
        }
    };

    println!("⚠️  警告: 您即将删除树 \"{}\"", tree_name);
    println!("这将永久删除该树中的所有数据，此操作不可撤销！");
    print!("请输入 'yes' 确认删除: ");

    use std::io::{self, Write};
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();

    if input != "yes" {
        println!("❌ 操作已取消");
        return Ok(());
    }

    // 检查树是否存在
    let tree_names = db.tree_names();
    let tree_name_bytes = tree_name.as_bytes();

    if !tree_names.iter().any(|name| name == tree_name_bytes) {
        println!("❌ 树 \"{}\" 不存在", tree_name);
        println!("使用 'list' 命令查看可用的树");
        return Ok(());
    }

    // 删除树
    match db.drop_tree(tree_name_bytes) {
        Ok(true) => {
            println!("✅ 树 \"{}\" 已成功删除", tree_name);
        },
        Ok(false) => {
            println!("❌ 树 \"{}\" 不存在或已被删除", tree_name);
        },
        Err(e) => {
            println!("❌ 删除树失败: {}", e);
        }
    }

    Ok(())
}

/// 清空指定树的所有数据
fn clear_tree(db: &sled::Db, tree_name: Option<&String>) -> Result<()> {
    let tree_name = match tree_name {
        Some(name) => name,
        None => {
            println!("❌ 请指定要清空的树名");
            println!("使用 'list' 命令查看可用的树");
            return Ok(());
        }
    };

    // 检查树是否存在
    let tree_names = db.tree_names();
    let tree_name_bytes = tree_name.as_bytes();

    if !tree_names.iter().any(|name| name == tree_name_bytes) {
        println!("❌ 树 \"{}\" 不存在", tree_name);
        println!("使用 'list' 命令查看可用的树");
        return Ok(());
    }

    println!("⚠️  警告: 您即将清空树 \"{}\" 中的所有数据", tree_name);
    println!("这将删除该树中的所有记录，但保留树结构，此操作不可撤销！");
    print!("请输入 'yes' 确认清空: ");

    use std::io::{self, Write};
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();

    if input != "yes" {
        println!("❌ 操作已取消");
        return Ok(());
    }

    // 打开指定的树
    let tree = match db.open_tree(tree_name.as_bytes()) {
        Ok(tree) => tree,
        Err(e) => {
            println!("❌ 无法打开树 \"{}\": {}", tree_name, e);
            return Ok(());
        }
    };

    let record_count_before = tree.len();

    // 清空树
    match tree.clear() {
        Ok(_) => {
            // 刷新到磁盘
            tree.flush()?;
            println!("✅ 树 \"{}\" 已成功清空", tree_name);
            println!("   删除了 {} 条记录", record_count_before);
        },
        Err(e) => {
            println!("❌ 清空树失败: {}", e);
        }
    }

    Ok(())
}
