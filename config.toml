# ========================================
# Solana Pump.fun 价格监控器配置文件
# ========================================

# HTTP RPC 配置
[rpc]
# Solana HTTP RPC 端点
endpoint = "https://solana-rpc.publicnode.com"
# 请求超时时间（秒）
timeout_seconds = 30

# gRPC 连接配置
[grpc]
# Yellowstone gRPC 端点地址 - 通过代理访问
# 注意：此地址不可修改，必须使用指定的 publicnode 端点
endpoint = "https://solana-yellowstone-grpc.publicnode.com:443"
# 连接超时时间（秒）
timeout_seconds = 30
# 是否使用 TLS 加密连接
use_tls = true
# gRPC 认证令牌（可选）
# x_token = "your_token_here"

# 监控配置
[monitoring]
# 要监控的token地址列表（请替换为您要监控的实际代币地址）
token_addresses = [
    "7RDRRxLH1rAqKq2nVvBq7BjfiSJdVxa2GSqrZZvEpump"
]
# 监控间隔时间（秒）
monitor_interval_seconds = 1
# 价格变化阈值（百分比）- 只有超过此阈值才会触发价格变化事件
price_change_threshold = 0.01
# 是否启用实时流监听
enable_stream_monitoring = true

# 日志配置
[logging]
# 日志级别：trace, debug, info, warn, error
# debug: 显示详细的调试信息，包括所有请求和响应
# info: 显示基本的运行信息
# warn: 只显示警告和错误
# error: 只显示错误信息
level = "debug"
# 是否在控制台显示日志
console = true
# 日志文件路径（可选，取消注释以启用文件日志）
# file_path = "logs/price_watcher.log"

# 代理配置
[proxy]
# 是否启用代理
enabled = true
# 代理服务器地址
host = "************"
# 代理服务器端口
port = 7897
# 代理用户名（可选）
# username = "proxy_user"
# 代理密码（可选）
# password = "proxy_pass"

# 网络配置
[network]
# 最大重试次数
max_retry_attempts = 2
# 重试间隔（毫秒）
retry_interval_ms = 1000
# 连接超时时间（毫秒）
connection_timeout_ms = 3000

# 数据库配置
[database]
# 是否启用数据库缓存
enabled = true
# 数据库文件路径
path = "db/pool_cache"
