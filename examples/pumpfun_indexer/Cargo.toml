[package]
name = "grpc-raydium-pool-monitoring-rust"
version = "1.0.0"
authors = ["adilcpm"]
edition = "2021"
license = "Apache-2.0"

[dependencies]
anyhow = "1.0.62"
backoff = { version = "0.4.0", features = ["tokio"] }
bincode = "1.3.3"
bs58 = "0.5.1"
chrono = "0.4.39"
clap = { version = "4.3.0", features = ["derive"] }
env_logger = "0.11.3"
futures = "0.3.24"
hex = "0.4.3"
log = "0.4.17"
maplit = "1.0.2"
serde_json = "1.0.135"
solana-sdk = "2.1.7"
solana-transaction-status = "2.1.7"
solana-program = "2.1.7"
solana-account-decoder-client-types = "2.1.7"
tokio = { version = "1.21.2", features = ["rt-multi-thread", "fs"] }
openssl = { version = "0.10", features = ["vendored"] }
tonic = "0.12.1"
yellowstone-grpc-client = "4.0.0"
yellowstone-grpc-proto = { version = "4.0.0", default-features = false ,features = ["plugin"] }
yellowstone-vixen-core = { git = "https://github.com/rpcpool/yellowstone-vixen" }
yellowstone-vixen-parser = { git = "https://github.com/rpcpool/yellowstone-vixen", features = ["raydium"] }
indicatif = "0.17.9"
pump_interface = { path = "./parsers/pump_interface", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
serde_with = "3.0"
postgres = "0.19"
spl-token = "7.0.0"
solana-client = "2.1.7"
