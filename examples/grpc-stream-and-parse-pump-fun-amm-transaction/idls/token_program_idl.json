{"version": "3.3.0", "name": "spl_token", "instructions": [{"name": "initializeMint", "accounts": [{"name": "mint", "isMut": true, "isis_signer": false}, {"name": "rent", "isMut": false, "isis_signer": false}], "args": [{"name": "decimals", "type": "u8"}, {"name": "mintAuthority", "type": "public<PERSON>ey"}, {"name": "freezeAuthority", "type": {"option": "public<PERSON>ey"}}]}, {"name": "initializeAccount", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": false}, {"name": "rent", "isMut": false, "isis_signer": false}], "args": []}, {"name": "initializeMultisig", "accounts": [{"name": "multisig", "isMut": true, "isis_signer": false}, {"name": "rent", "isMut": false, "isis_signer": false}], "args": [{"name": "m", "type": "u8"}]}, {"name": "transfer", "accounts": [{"name": "source", "isMut": true, "isis_signer": false}, {"name": "destination", "isMut": true, "isis_signer": false}, {"name": "authority", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "approve", "accounts": [{"name": "source", "isMut": true, "isis_signer": false}, {"name": "delegate", "isMut": false, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "revoke", "accounts": [{"name": "source", "isMut": true, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": []}, {"name": "setAuthority", "accounts": [{"name": "owned", "isMut": true, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}, {"name": "is_signer", "isMut": false, "isis_signer": true}], "args": [{"name": "authorityType", "type": {"defined": "AuthorityType"}}, {"name": "newAuthority", "type": {"option": "public<PERSON>ey"}}]}, {"name": "mintTo", "accounts": [{"name": "mint", "isMut": true, "isis_signer": false}, {"name": "account", "isMut": true, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "burn", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": true, "isis_signer": false}, {"name": "authority", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "closeAccount", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "destination", "isMut": true, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": []}, {"name": "freezeAccount", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": []}, {"name": "thawAccount", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": []}, {"name": "transferChecked", "accounts": [{"name": "source", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "destination", "isMut": true, "isis_signer": false}, {"name": "authority", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}, {"name": "decimals", "type": "u8"}]}, {"name": "approveChecked", "accounts": [{"name": "source", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "delegate", "isMut": false, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}, {"name": "decimals", "type": "u8"}]}, {"name": "mintToChecked", "accounts": [{"name": "mint", "isMut": true, "isis_signer": false}, {"name": "account", "isMut": true, "isis_signer": false}, {"name": "owner", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}, {"name": "decimals", "type": "u8"}]}, {"name": "burnChecked", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": true, "isis_signer": false}, {"name": "authority", "isMut": false, "isis_signer": true}], "args": [{"name": "amount", "type": "u64"}, {"name": "decimals", "type": "u8"}]}, {"name": "initializeAccount2", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}, {"name": "rent", "isMut": false, "isis_signer": false}], "args": [{"name": "owner", "type": "public<PERSON>ey"}]}, {"name": "syncNative", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}], "args": []}, {"name": "initializeAccount3", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}, {"name": "mint", "isMut": false, "isis_signer": false}], "args": [{"name": "owner", "type": "public<PERSON>ey"}]}, {"name": "initializeMultisig2", "accounts": [{"name": "multisig", "isMut": true, "isis_signer": false}, {"name": "is_signer", "isMut": false, "isis_signer": false}], "args": [{"name": "m", "type": "u8"}]}, {"name": "initializeMint2", "accounts": [{"name": "mint", "isMut": true, "isis_signer": false}], "args": [{"name": "decimals", "type": "u8"}, {"name": "mintAuthority", "type": "public<PERSON>ey"}, {"name": "freezeAuthority", "type": {"option": "public<PERSON>ey"}}]}, {"name": "getAccountDataSize", "accounts": [{"name": "mint", "isMut": false, "isis_signer": false}], "args": []}, {"name": "initializeImmutableOwner", "accounts": [{"name": "account", "isMut": true, "isis_signer": false}], "args": []}, {"name": "amountToUiAmount", "accounts": [{"name": "mint", "isMut": false, "isis_signer": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "uiAmountToAmount", "accounts": [{"name": "mint", "isMut": false, "isis_signer": false}], "args": [{"name": "uiAmount", "type": "u64"}]}], "accounts": [{"name": "Mint", "type": {"kind": "struct", "fields": [{"name": "mintAuthority", "type": {"option": "public<PERSON>ey"}}, {"name": "supply", "type": "u64"}, {"name": "decimals", "type": "u8"}, {"name": "isInitialized", "type": "bool"}, {"name": "freezeAuthority", "type": {"option": "public<PERSON>ey"}}]}}, {"name": "Account", "type": {"kind": "struct", "fields": [{"name": "mint", "type": "public<PERSON>ey"}, {"name": "owner", "type": "public<PERSON>ey"}, {"name": "amount", "type": "u64"}, {"name": "delegate", "type": {"option": "public<PERSON>ey"}}, {"name": "state", "type": {"defined": "AccountState"}}, {"name": "isNative", "type": {"option": "u64"}}, {"name": "delegated<PERSON><PERSON>", "type": "u64"}, {"name": "closeAuthority", "type": {"option": "public<PERSON>ey"}}]}}, {"name": "Multisig", "type": {"kind": "struct", "fields": [{"name": "m", "type": "u8"}, {"name": "n", "type": "u8"}, {"name": "isInitialized", "type": "bool"}, {"name": "is_signers", "type": {"array": ["public<PERSON>ey", 11]}}]}}], "types": [{"name": "AccountState", "type": {"kind": "enum", "variants": [{"name": "Uninitialized"}, {"name": "Initialized"}, {"name": "Frozen"}]}}, {"name": "AuthorityType", "type": {"kind": "enum", "variants": [{"name": "MintTokens"}, {"name": "FreezeA<PERSON>unt"}, {"name": "Account<PERSON><PERSON><PERSON>"}, {"name": "CloseAccount"}]}}], "errors": [{"code": 0, "name": "NotRentExempt", "msg": "Lamport balance below rent-exempt threshold"}, {"code": 1, "name": "InsufficientFunds", "msg": "Insufficient funds"}, {"code": 2, "name": "InvalidMint", "msg": "Invalid Mint"}, {"code": 3, "name": "MintMismatch", "msg": "Account not associated with this Mint"}, {"code": 4, "name": "OwnerMismatch", "msg": "Owner does not match"}, {"code": 5, "name": "FixedSupply", "msg": "Fixed supply"}, {"code": 6, "name": "AlreadyInUse", "msg": "Already in use"}, {"code": 7, "name": "InvalidNumberOfProvidedis_signers", "msg": "Invalid number of provided is_signers"}, {"code": 8, "name": "InvalidNumberOfRequiredis_signers", "msg": "Invalid number of required is_signers"}, {"code": 9, "name": "UninitializedState", "msg": "State is unititialized"}, {"code": 10, "name": "NativeNotSupported", "msg": "Instruction does not support native tokens"}, {"code": 11, "name": "NonNativeHasBalance", "msg": "Non-native account can only be closed if its balance is zero"}, {"code": 12, "name": "InvalidInstruction", "msg": "Invalid instruction"}, {"code": 13, "name": "InvalidState", "msg": "State is invalid for requested operation"}, {"code": 14, "name": "Overflow", "msg": "Operation overflowed"}, {"code": 15, "name": "AuthorityTypeNotSupported", "msg": "Account does not support specified authority type"}, {"code": 16, "name": "MintCannotFreeze", "msg": "This token mint cannot freeze accounts"}, {"code": 17, "name": "Account<PERSON><PERSON><PERSON>", "msg": "Account is frozen"}, {"code": 18, "name": "MintDecimalsMismatch", "msg": "The provided decimals value different from the Mint decimals"}, {"code": 19, "name": "NonNativeNotSupported", "msg": "Instruction does not support non-native tokens"}], "metadata": {"address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}}