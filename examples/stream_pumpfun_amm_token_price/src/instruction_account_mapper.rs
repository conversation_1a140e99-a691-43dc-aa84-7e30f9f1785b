// 导入序列化工具
use crate::serialization::serialize_pubkey;
// 导入序列化和反序列化 trait
use serde::{Deserialize, Serialize};
// 导入 Solana 程序相关类型
use solana_program::{program_error::ProgramError, pubkey::Pubkey};
use solana_sdk::instruction::AccountMeta;

/// IDL 指令定义结构体
/// 表示 IDL 文件中的一个指令定义
#[derive(Debug, Deserialize)]
struct IdlInstruction {
    /// 指令名称
    name: String,
    /// 指令需要的账户列表
    accounts: Vec<IdlAccount>,
}

/// IDL 账户定义结构体
/// 表示指令中单个账户的定义
#[derive(Debug, Deserialize)]
struct IdlAccount {
    /// 账户名称
    name: String,
    /// 账户是否可写（可选字段）
    #[serde(default, rename = "is_writable")]
    is_writable: Option<bool>,
    /// 账户是否需要签名（可选字段）
    #[serde(default, rename = "is_signer")]
    is_signer: Option<bool>,
}

/// IDL 文件结构体
/// 表示完整的接口描述语言文件
#[derive(Debug, Deserialize)]
pub struct Idl {
    /// 所有指令的定义列表
    instructions: Vec<IdlInstruction>,
}

/// 账户元数据结构体
/// 包含账户的完整信息，用于输出和分析
#[derive(Debug, Serialize, Clone, PartialEq)]
pub struct AccountMetadata {
    /// 账户公钥
    #[serde(serialize_with = "serialize_pubkey")]
    pub pubkey: Pubkey,
    /// 账户是否可写
    pub is_writable: bool,
    /// 账户是否为签名者
    pub is_signer: bool,
    /// 账户名称（来自 IDL 定义）
    pub name: String,
}

/// 指令账户映射器 trait
/// 定义了将 IDL 定义映射到实际账户的接口
pub trait InstructionAccountMapper<'info> {
    /// 将实际账户映射到 IDL 定义的账户
    ///
    /// # 参数
    /// * `accounts` - 实际交易中的账户列表
    /// * `instruction_name` - 指令名称
    ///
    /// # 返回值
    /// * `Ok(Vec<AccountMetadata>)` - 映射成功的账户元数据列表
    /// * `Err(ProgramError)` - 映射失败时的错误
    fn map_accounts<'me>(
        &self,
        accounts: &[AccountMeta],
        instruction_name: &str,
    ) -> Result<Vec<AccountMetadata>, ProgramError>;
}

/// 为 IDL 结构体实现账户映射功能
impl<'info> InstructionAccountMapper<'info> for Idl {
    /// 将实际账户与 IDL 定义进行映射
    ///
    /// # 工作流程
    /// 1. 根据指令名称查找 IDL 中的指令定义
    /// 2. 将实际账户与 IDL 定义的账户一一对应
    /// 3. 为 IDL 中未定义的额外账户生成默认名称
    /// 4. 返回完整的账户元数据列表
    fn map_accounts<'me>(
        &self,
        accounts: &[AccountMeta],
        instruction_name: &str,
    ) -> Result<Vec<AccountMetadata>, ProgramError> {
        // 查找指令定义（不区分大小写）
        let instruction = self
            .instructions
            .iter()
            .find(|ix| ix.name.to_lowercase() == instruction_name.to_lowercase())
            .ok_or(ProgramError::InvalidArgument)?;

        // 映射 IDL 中定义的账户
        let mut account_metadata: Vec<AccountMetadata> = accounts
            .iter()
            .take(instruction.accounts.len())  // 只取 IDL 中定义的账户数量
            .enumerate()
            .map(|(i, account)| {
                let account_info = &instruction.accounts[i];
                AccountMetadata {
                    pubkey: account.pubkey,
                    // 使用 IDL 中的权限定义，如果未定义则默认为 false
                    is_writable: account_info.is_writable.unwrap_or(false),
                    is_signer: account_info.is_signer.unwrap_or(false),
                    name: account_info.name.clone(),
                }
            })
            .collect();

        // 处理 IDL 中未定义的剩余账户
        for (i, account) in accounts.iter().enumerate().skip(instruction.accounts.len()) {
            account_metadata.push(AccountMetadata {
                pubkey: account.pubkey,
                // 对于剩余账户，使用实际的权限信息
                is_writable: account.is_writable,
                is_signer: account.is_signer,
                // 生成默认名称
                name: format!("Remaining accounts {}", i - instruction.accounts.len() + 1),
            });
        }

        Ok(account_metadata)
    }
}
