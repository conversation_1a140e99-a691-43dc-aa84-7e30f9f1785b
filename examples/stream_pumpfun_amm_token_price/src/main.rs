// 导入本地模块
mod event_account_mapper;
mod instruction_account_mapper; // 指令账户映射模块
mod serialization; // 序列化工具模块
mod token_serializable; // 代币序列化模块 // 事件账户映射模块

use {
    // 重试机制相关
    backoff::{future::retry, ExponentialBackoff},
    // 命令行参数解析
    clap::Parser as C<PERSON><PERSON><PERSON><PERSON>,
    // 异步流处理
    futures::{future::TryFutureExt, sink::SinkExt, stream::StreamExt},
    instruction_account_mapper::{AccountMetadata, Idl, InstructionAccountMapper},
    // 日志记录
    log::{error, info},
    // PumpFun AMM 接口
    pumpfun_amm_interface::instructions::PumpfunAmmProgramIx,
    // 序列化支持
    serde::Serialize,
    // 本地模块导入
    serialization::{serialize_option_pubkey, serialize_pubkey},
    // Solana 相关类型
    solana_account_decoder_client_types::token::UiTokenAmount,
    solana_sdk::{
        hash::Hash,
        instruction::{AccountMeta, CompiledInstruction, Instruction},
        message::{
            v0::{LoadedAddresses, Message, MessageAddressTableLookup},
            MessageHeader, VersionedMessage,
        },
        pubkey::Pubkey,
        signature::Signature,
        transaction::VersionedTransaction,
        transaction_context::TransactionReturnData,
    },
    // Solana 交易状态相关
    solana_transaction_status::{
        ConfirmedTransactionWithStatusMeta, InnerInstruction, InnerInstructions, Reward,
        RewardType, TransactionStatusMeta, TransactionTokenBalance, TransactionWithStatusMeta,
        VersionedTransactionWithStatusMeta,
    },

    // 标准库
    std::{
        collections::HashMap,
        env, fs,
        str::FromStr,
        sync::Arc,
        time::{Duration, SystemTime, UNIX_EPOCH},
    },
    // 异步运行时
    tokio::sync::Mutex,
    // gRPC 客户端
    tonic::transport::channel::ClientTlsConfig,
    // Yellowstone gRPC 客户端
    yellowstone_grpc_client::{GeyserGrpcClient, Interceptor},
    yellowstone_grpc_proto::{
        geyser::SubscribeRequestFilterTransactions,
        prelude::{
            subscribe_update::UpdateOneof, CommitmentLevel, SubscribeRequest, SubscribeRequestPing,
        },
    },
};

// SPL Token 指令
use spl_token::instruction::TokenInstruction;
// 代币序列化转换
use crate::token_serializable::convert_to_serializable;

// Solana 交易状态和结果类型
use solana_sdk::transaction::Result as TransactionResult;
use solana_transaction_status::Rewards;

// 事件解码相关
use crate::event_account_mapper::decode_event_data;
use crate::event_account_mapper::AccountEventError;
use crate::event_account_mapper::DecodedEvent;

/// 交易过滤器映射类型别名
/// 用于存储不同客户端的交易订阅过滤器配置
type TxnFilterMap = HashMap<String, SubscribeRequestFilterTransactions>;

/// Solana SPL Token 程序的程序 ID
/// 这是 Solana 上标准代币程序的地址
//#config# TOKEN_PROGRAM_ID - Solana 标准代币程序地址，通常不需要修改
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";

/// PumpFun AMM 程序的程序 ID
/// 这是 PumpFun 自动做市商程序的地址
//#config# PUMPFUN_AMM_PROGRAM_ID - PumpFun AMM 程序地址，如果要监听其他 AMM 可以修改这里
const PUMPFUN_AMM_PROGRAM_ID: &str = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";
//#config# TOKEN_ACCOUNT_ID - token 程序地址，如果要监听其他 token 可以修改这里
const TOKEN_ACCOUNT_ID: &str = "5ApSDnWK155kcqnrs5ZYTrVMN7wVeoXNQnrh696Vpump";
/// 命令行参数结构体
/// 定义了程序启动时需要的配置参数
#[derive(Debug, Clone, ClapParser)]
#[clap(author, version, about)]
struct Args {
    /// gRPC 端点地址
    /// Solana 节点的 gRPC 服务地址，用于接收实时数据
    #[clap(short, long, help = "gRPC endpoint")]
    endpoint: String,

    /// 认证令牌（可选）
    /// 用于访问 gRPC 服务的认证令牌，某些公共节点不需要
    #[clap(long, help = "X-Token (optional for some public nodes)")]
    x_token: Option<String>,

    /// HTTP 代理地址（可选）
    /// 格式：http://host:port 或 socks5://host:port
    #[clap(long, help = "HTTP/SOCKS5 proxy URL (e.g., http://************:7897)")]
    proxy: Option<String>,
}

impl Args {
    /// 建立与 Solana gRPC 节点的连接
    ///
    /// # 返回值
    /// * `Ok(GeyserGrpcClient)` - 成功建立的 gRPC 客户端
    /// * `Err(anyhow::Error)` - 连接失败时的错误
    ///
    /// # 配置说明
    /// - 连接超时：10秒
    /// - 请求超时：10秒
    /// - TLS 配置：使用系统根证书
    /// - 最大消息大小：1GB
    /// - 支持代理连接
    async fn connect(&self) -> anyhow::Result<GeyserGrpcClient<impl Interceptor>> {
        use std::env;

        log::info!("Attempting to connect to: {}", self.endpoint);

        // 设置代理环境变量（如果提供了代理）
        if let Some(proxy_url) = &self.proxy {
            env::set_var("HTTP_PROXY", proxy_url);
            env::set_var("HTTPS_PROXY", proxy_url);
            log::info!("Using proxy: {}", proxy_url);
        }

        // 尝试解析端点 URL 以提供更好的错误信息
        let endpoint_url =
            if self.endpoint.starts_with("http://") || self.endpoint.starts_with("https://") {
                self.endpoint.clone()
            } else {
                format!("https://{}", self.endpoint)
            };

        log::info!("Parsed endpoint URL: {}", endpoint_url);

        let mut builder = GeyserGrpcClient::build_from_shared(endpoint_url.clone())
            .map_err(|e| anyhow::anyhow!("Failed to build gRPC client: {}", e))?;

        // 只有在提供了 token 时才设置认证令牌
        if let Some(token) = &self.x_token {
            if !token.is_empty() {
                builder = builder
                    .x_token(Some(token.clone()))
                    .map_err(|e| anyhow::anyhow!("Failed to set authentication token: {}", e))?;
                log::info!("Using authentication token");
            } else {
                log::info!("No authentication token provided");
            }
        } else {
            log::info!("No authentication token provided");
        }

        log::info!("Configuring connection parameters...");

        let result = builder
            .connect_timeout(Duration::from_secs(30)) // 增加连接超时
            .timeout(Duration::from_secs(30)) // 增加请求超时
            .tls_config(ClientTlsConfig::new().with_native_roots())
            .map_err(|e| anyhow::anyhow!("Failed to configure TLS: {}", e))? // TLS 配置
            .max_decoding_message_size(1024 * 1024) // 最大消息大小 1024KB
            .connect()
            .await;

        match result {
            Ok(client) => {
                log::info!("Successfully connected to gRPC endpoint");
                Ok(client)
            }
            Err(e) => {
                log::error!("Failed to connect to gRPC endpoint: {}", e);
                log::error!("Endpoint: {}", endpoint_url);
                if let Some(proxy) = &self.proxy {
                    log::error!("Proxy: {}", proxy);
                }
                Err(anyhow::anyhow!("gRPC connection failed: {}", e))
            }
        }
    }

    /// 创建交易订阅请求配置
    ///
    /// # 返回值
    /// * `Ok(SubscribeRequest)` - 配置好的订阅请求
    /// * `Err(anyhow::Error)` - 配置失败时的错误
    ///
    /// # 过滤器配置
    /// - 排除投票交易（vote: false）
    /// - 排除失败交易（failed: false）
    /// - 只包含 PumpFun AMM 程序的交易
    /// - 使用 Processed 确认级别获得最快响应
    pub fn get_txn_updates(&self) -> anyhow::Result<SubscribeRequest> {
        let mut transactions: TxnFilterMap = HashMap::new();

        //#config# 交易过滤器配置 - 可以修改过滤条件
        transactions.insert(
            "client".to_owned(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),   // 排除投票交易
                failed: Some(false), // 排除失败交易
                //#config# account_include - 只监听指定程序的交易，可以添加其他程序 ID
                account_include: vec![PUMPFUN_AMM_PROGRAM_ID.to_string()], // 只监听 PumpFun AMM
                account_exclude: vec![],                                   // 不排除任何账户
                account_required: vec![TOKEN_ACCOUNT_ID.to_string()],      // 不要求特定账户
                signature: None,                                           // 不过滤特定签名
            },
        );

        // 构建完整的订阅请求
        Ok(SubscribeRequest {
            accounts: HashMap::default(),            // 不订阅账户更新
            slots: HashMap::default(),               // 不订阅插槽更新
            transactions,                            // 交易订阅配置
            transactions_status: HashMap::default(), // 不订阅交易状态
            blocks: HashMap::default(),              // 不订阅区块
            blocks_meta: HashMap::default(),         // 不订阅区块元数据
            entry: HashMap::default(),               // 不订阅条目
            //#config# commitment - 确认级别：Processed(最快), Confirmed(中等), Finalized(最安全)
            commitment: Some(CommitmentLevel::Processed as i32), // 使用 Processed 确认级别
            accounts_data_slice: Vec::default(),                 // 不切片账户数据
            ping: None,                                          // 不设置 ping
            from_slot: None,                                     // 从当前插槽开始
        })
    }
}

/// PumpFun AMM 交换输出数据结构
/// 包含交换操作后的关键信息，用于价格计算和监控
#[derive(Debug, Clone)]
struct PumpAmmSwapOutput {
    /// 基础代币的铸币地址
    pub base_mint: String,
    /// 报价代币的铸币地址（通常是 SOL）
    pub quote_mint: String,
    /// 流动性池中基础代币的储备量
    pub pool_base_token_reserve: String,
    /// 流动性池中报价代币的储备量
    pub pool_quote_token_reserve: String,
    /// 计算出的代币价格（报价代币/基础代币）
    pub price: String,
}

/// 带有父程序信息的交易指令
/// 用于跟踪指令的调用层次关系
#[derive(Debug, Serialize)]
struct TransactionInstructionWithParent {
    /// 指令本身
    instruction: Instruction,
    /// 调用此指令的父程序 ID（如果是内部指令）
    parent_program_id: Option<Pubkey>,
}

/// 解码后的指令数据结构
/// 包含指令的所有相关信息，便于分析和处理
#[derive(Clone, Debug, Serialize, PartialEq)]
pub struct DecodedInstruction {
    /// 指令名称（如 "buy", "sell", "createPool" 等）
    pub name: String,
    /// 指令涉及的账户列表及其元数据
    pub accounts: Vec<AccountMetadata>,
    /// 指令的参数数据（JSON 格式）
    pub data: serde_json::Value,
    /// 关联的事件数据（如果有）
    pub event: Option<DecodedEvent>,
    /// 执行此指令的程序 ID
    #[serde(serialize_with = "serialize_pubkey")]
    pub program_id: Pubkey,
    /// 调用此指令的父程序 ID（用于内部指令）
    #[serde(serialize_with = "serialize_option_pubkey")]
    pub parent_program_id: Option<Pubkey>,
}

#[derive(Debug)]
pub struct TransactionWithActions {
    pub slot: u64,
    pub tx_with_meta: TransactionWithStatusMeta,
    pub block_time: i64,
    pub actions: Vec<DecodedInstruction>,
}

#[derive(Clone, Debug, PartialEq)]
pub struct ParsedTransaction {
    pub signatures: Vec<Signature>,
    pub message: ParsedMessage,
}

#[derive(Clone, Debug, PartialEq)]
pub struct ParsedMessage {
    pub header: MessageHeader,
    pub account_keys: Vec<Pubkey>,
    pub recent_blockhash: Hash,
    pub instructions: Vec<DecodedInstruction>,
    pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

#[derive(Clone, Debug, PartialEq)]
pub struct ParsedTransactionStatusMeta {
    pub status: TransactionResult<()>,
    pub fee: u64,
    pub pre_balances: Vec<u64>,
    pub post_balances: Vec<u64>,
    pub inner_instructions: Vec<DecodedInstruction>,
    pub log_messages: Option<Vec<String>>,
    pub pre_token_balances: Option<Vec<TransactionTokenBalance>>,
    pub post_token_balances: Option<Vec<TransactionTokenBalance>>,
    pub rewards: Option<Rewards>,
    pub loaded_addresses: LoadedAddresses,
    pub return_data: Option<TransactionReturnData>,
    pub compute_units_consumed: Option<u64>,
}

#[derive(Clone, Debug, PartialEq)]
pub struct ParsedConfirmedTransaction {
    pub slot: u64,
    pub transaction: ParsedTransaction,
    pub meta: ParsedTransactionStatusMeta,
    pub block_time: Option<i64>,
}
#[derive(Debug)]
pub struct ParsedConfirmedTransactionWithStatusMeta {
    pub slot: u64,
    pub transaction: ParsedTransaction,
    pub meta: ParsedTransactionStatusMeta,
    pub block_time: Option<i64>,
}

/// 程序主入口点
///
/// 初始化日志系统，解析命令行参数，并启动带有重试机制的 gRPC 连接
///
/// # 工作流程
/// 1. 设置日志级别（默认为 info）
/// 2. 初始化环境日志记录器
/// 3. 解析命令行参数
/// 4. 使用指数退避策略重试连接
/// 5. 建立 gRPC 连接并开始订阅交易数据
///
/// # 返回值
/// * `Ok(())` - 程序正常退出
/// * `Err(anyhow::Error)` - 程序执行过程中发生错误
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 设置日志级别，如果环境变量未设置则默认为 "info"
    env::set_var(
        env_logger::DEFAULT_FILTER_ENV,
        env::var_os(env_logger::DEFAULT_FILTER_ENV).unwrap_or_else(|| "info".into()),
    );
    // 初始化环境日志记录器
    env_logger::init();

    // 解析命令行参数
    let args = Args::parse();
    // 用于跟踪是否为首次连接尝试的标志
    let zero_attempts = Arc::new(Mutex::new(true));

    // 使用指数退避策略进行重试连接
    retry(ExponentialBackoff::default(), move || {
        let args = args.clone();
        let zero_attempts = Arc::clone(&zero_attempts);

        async move {
            // 检查是否为首次连接尝试
            let mut zero_attempts = zero_attempts.lock().await;
            if *zero_attempts {
                *zero_attempts = false;
            } else {
                info!("Retry to connect to the server");
            }
            drop(zero_attempts);

            // 尝试建立 gRPC 连接
            let client = args.connect().await.map_err(backoff::Error::transient)?;
            info!("Connected");

            // 创建订阅请求配置
            let request = args.get_txn_updates().map_err(backoff::Error::Permanent)?;

            // 开始订阅和处理交易数据
            geyser_subscribe(client, request)
                .await
                .map_err(backoff::Error::transient)?;

            Ok::<(), backoff::Error<anyhow::Error>>(())
        }
        // 记录连接失败的错误信息
        .inspect_err(|error| error!("failed to connect: {error}"))
    })
    .await
    .map_err(Into::into)
}

/// 将字符串转换为驼峰命名法
///
/// 将首字母转换为小写，其余部分保持不变
///
/// # 参数
/// * `name` - 要转换的字符串
///
/// # 返回值
/// * `String` - 转换后的驼峰命名字符串
///
/// # 示例
/// ```
/// assert_eq!(to_camel_case("Transfer"), "transfer");
/// assert_eq!(to_camel_case("InitializeMint"), "initializeMint");
/// ```
fn to_camel_case(name: &str) -> String {
    let mut chars = name.chars();
    match chars.next() {
        Some(first_char) => first_char.to_lowercase().collect::<String>() + chars.as_str(),
        None => String::new(),
    }
}

/// 从 TokenInstruction 中提取指令名称
///
/// 解析 TokenInstruction 的 Debug 字符串表示，提取指令名称并转换为驼峰命名法
///
/// # 参数
/// * `instruction` - SPL Token 指令引用
///
/// # 返回值
/// * `String` - 格式化后的指令名称
///
/// # 工作原理
/// 1. 将指令转换为 Debug 字符串格式
/// 2. 查找第一个 " {" 来分离指令名称和参数
/// 3. 将指令名称转换为驼峰命名法
fn get_instruction_name_with_typename(instruction: &TokenInstruction) -> String {
    let debug_string = format!("{:?}", instruction);
    if let Some(first_brace) = debug_string.find(" {") {
        // 提取 " {" 之前的部分作为指令名称
        let name = &debug_string[..first_brace];
        to_camel_case(name)
    } else {
        // 如果没有找到 " {"，则整个字符串就是指令名称
        to_camel_case(&debug_string)
    }
}

/// 处理单个交易更新
///
/// 将原始的 gRPC 交易数据转换为 Solana 交易结构，
/// 解码指令，并分析 PumpFun AMM 交换操作
///
/// # 参数
/// * `update` - 来自 gRPC 流的交易更新数据
///
/// # 返回值
/// * `Ok(())` - 交易处理成功
/// * `Err(anyhow::Error)` - 处理过程中发生错误
async fn process_transaction_update(
    update: yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction,
) -> anyhow::Result<()> {
    let slot = update.slot;
    let block_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs() as i64;

    let txn = update
        .transaction
        .ok_or_else(|| anyhow::anyhow!("Transaction data is empty"))?;

    // 转换原始交易数据为 Solana 交易结构
    let confirmed_txn_with_meta = convert_raw_transaction_to_confirmed(slot, block_time, txn)?;

    // 提取并解码事件数据
    let decoded_event = extract_event_from_transaction(&confirmed_txn_with_meta)?;

    // 获取编译指令和内部指令
    let compiled_instructions = get_compiled_instructions(&confirmed_txn_with_meta);
    let inner_instructions = get_inner_instructions(&confirmed_txn_with_meta);

    // 解码指令
    let decoded_compiled_instructions =
        decode_instructions(&compiled_instructions, &decoded_event)?;
    let decoded_inner_instructions = decode_instructions(&inner_instructions, &decoded_event)?;

    // 构建解析后的交易结构
    let parsed_txn = build_parsed_transaction(
        slot,
        block_time,
        &confirmed_txn_with_meta,
        decoded_compiled_instructions.clone(),
        decoded_inner_instructions.clone(),
    )?;

    // 分析交换输出
    if let Some(swap_output) = parse_swap_transaction_output(
        parsed_txn,
        decoded_inner_instructions,
        decoded_compiled_instructions,
    ) {
        // 获取交易签名用于日志
        if let TransactionWithStatusMeta::Complete(versioned_tx) =
            &confirmed_txn_with_meta.tx_with_meta
        {
            if let Some(signature) = versioned_tx.transaction.signatures.first() {
                info!("signature: {}", signature);
                info!("PumpAmmSwapOutput: {:#?}", swap_output);
            }
        }
    }

    Ok(())
}

/// 订阅并处理 Solana gRPC 数据流
///
/// 这是程序的核心函数，负责：
/// 1. 建立与 Solana 节点的 gRPC 连接
/// 2. 订阅交易数据流
/// 3. 处理接收到的交易数据
/// 4. 解码和分析 PumpFun AMM 交易
///
/// # 参数
/// * `client` - 已建立连接的 gRPC 客户端
/// * `request` - 订阅请求配置
///
/// # 返回值
/// * `Ok(())` - 数据流正常结束
/// * `Err(anyhow::Error)` - 处理过程中发生错误
async fn geyser_subscribe(
    mut client: GeyserGrpcClient<impl Interceptor>,
    request: SubscribeRequest,
) -> anyhow::Result<()> {
    let (mut subscribe_tx, mut stream) = client.subscribe_with_request(Some(request)).await?;

    info!("stream opened");

    while let Some(message) = stream.next().await {
        match message {
            Ok(msg) => match msg.update_oneof {
                Some(UpdateOneof::Transaction(update)) => {
                    // 处理交易更新
                    if let Err(e) = process_transaction_update(update).await {
                        error!("Failed to process transaction update: {}", e);
                        continue;
                    }
                }
                Some(UpdateOneof::Ping(_)) => {
                    // 响应 ping 消息以保持连接活跃
                    subscribe_tx
                        .send(SubscribeRequest {
                            ping: Some(SubscribeRequestPing { id: 1 }),
                            ..Default::default()
                        })
                        .await?;
                }
                Some(UpdateOneof::Pong(_)) => {
                    // 收到 pong 响应，连接正常
                }
                None => {
                    error!("update not found in the message");
                    break;
                }
                _ => {
                    // 其他类型的更新，暂时忽略
                }
            },
            Err(error) => {
                error!("gRPC stream error: {error:?}");
                break;
            }
        }
    }

    info!("stream closed");
    Ok(())
}

/// 将原始 gRPC 交易数据转换为 Solana ConfirmedTransactionWithStatusMeta 结构
///
/// # 参数
/// * `slot` - 交易所在的插槽号
/// * `block_time` - 区块时间戳
/// * `txn` - 原始 gRPC 交易数据
///
/// # 返回值
/// * `Ok(ConfirmedTransactionWithStatusMeta)` - 转换成功的交易结构
/// * `Err(anyhow::Error)` - 转换过程中发生错误
fn convert_raw_transaction_to_confirmed(
    slot: u64,
    block_time: i64,
    txn: yellowstone_grpc_proto::prelude::SubscribeUpdateTransactionInfo,
) -> anyhow::Result<ConfirmedTransactionWithStatusMeta> {
    let raw_signature = txn.signature.clone();
    let raw_transaction = txn
        .transaction
        .ok_or_else(|| anyhow::anyhow!("Transaction is empty"))?;
    let raw_message = raw_transaction
        .message
        .ok_or_else(|| anyhow::anyhow!("Message is empty"))?;
    let header = raw_message
        .header
        .ok_or_else(|| anyhow::anyhow!("Header is empty"))?;
    let meta = txn.meta.ok_or_else(|| anyhow::anyhow!("Meta is empty"))?;

    // 验证签名长度
    if raw_signature.len() != 64 {
        return Err(anyhow::anyhow!(
            "Signature must be exactly 64 bytes, got {}",
            raw_signature.len()
        ));
    }

    // 转换签名
    let raw_signature_array: [u8; 64] = raw_signature
        .clone()
        .try_into()
        .map_err(|_| anyhow::anyhow!("Failed to convert signature to [u8; 64]"))?;
    let signature = Signature::from(raw_signature_array);

    // 转换区块哈希
    let recent_blockhash = Hash::new_from_array(
        raw_message
            .recent_blockhash
            .clone()
            .try_into()
            .map_err(|_| anyhow::anyhow!("Failed to convert recent_blockhash to [u8; 32]"))?,
    );

    // 构建交易结构
    let confirmed_txn_with_meta = ConfirmedTransactionWithStatusMeta {
        slot,
        tx_with_meta: TransactionWithStatusMeta::Complete(VersionedTransactionWithStatusMeta {
            transaction: VersionedTransaction {
                signatures: vec![signature],
                message: VersionedMessage::V0(Message {
                    header: MessageHeader {
                        num_required_signatures: header.num_required_signatures as u8,
                        num_readonly_signed_accounts: header.num_readonly_signed_accounts as u8,
                        num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u8,
                    },
                    account_keys: raw_message
                        .account_keys
                        .iter()
                        .map(|k| k.clone().try_into())
                        .collect::<Result<Vec<[u8; 32]>, _>>()
                        .map_err(|_| anyhow::anyhow!("Failed to convert account keys"))?
                        .into_iter()
                        .map(Pubkey::new_from_array)
                        .collect(),
                    recent_blockhash,
                    instructions: raw_message
                        .instructions
                        .iter()
                        .map(|ix| CompiledInstruction {
                            program_id_index: ix.program_id_index as u8,
                            accounts: ix.accounts.clone(),
                            data: ix.data.clone(),
                        })
                        .collect(),
                    address_table_lookups: raw_message
                        .address_table_lookups
                        .iter()
                        .map(|l| -> anyhow::Result<MessageAddressTableLookup> {
                            Ok(MessageAddressTableLookup {
                                account_key: Pubkey::new_from_array(
                                    l.account_key.clone().try_into().map_err(|_| {
                                        anyhow::anyhow!("Failed to convert account_key")
                                    })?,
                                ),
                                writable_indexes: l.writable_indexes.clone(),
                                readonly_indexes: l.readonly_indexes.clone(),
                            })
                        })
                        .collect::<Result<Vec<_>, _>>()?,
                }),
            },
            meta: TransactionStatusMeta {
                status: Result::Ok(()),
                fee: meta.fee,
                pre_balances: meta.pre_balances.clone(),
                post_balances: meta.post_balances.clone(),
                inner_instructions: Some(
                    meta.inner_instructions
                        .iter()
                        .map(|f| InnerInstructions {
                            index: f.index as u8,
                            instructions: f
                                .instructions
                                .iter()
                                .map(|v| InnerInstruction {
                                    instruction: CompiledInstruction {
                                        program_id_index: v.program_id_index as u8,
                                        accounts: v.accounts.clone(),
                                        data: v.data.clone(),
                                    },
                                    stack_height: Some(v.stack_height.unwrap_or(1)),
                                })
                                .collect(),
                        })
                        .collect(),
                ),
                log_messages: Some(meta.log_messages.clone()),
                pre_token_balances: Some(
                    meta.pre_token_balances
                        .iter()
                        .map(|tb| TransactionTokenBalance {
                            account_index: tb.account_index as u8,
                            mint: tb.mint.clone(),
                            ui_token_amount: UiTokenAmount {
                                ui_amount: {
                                    let ui_token_amount =
                                        tb.ui_token_amount.clone().unwrap_or_default();
                                    if ui_token_amount.ui_amount == 0.0 {
                                        None
                                    } else {
                                        Some(ui_token_amount.ui_amount)
                                    }
                                },
                                decimals: tb.ui_token_amount.clone().unwrap_or_default().decimals
                                    as u8,
                                amount: tb.ui_token_amount.clone().unwrap_or_default().amount,
                                ui_amount_string: tb
                                    .ui_token_amount
                                    .clone()
                                    .unwrap_or_default()
                                    .ui_amount_string,
                            },
                            owner: tb.clone().owner,
                            program_id: tb.clone().program_id,
                        })
                        .collect(),
                ),
                post_token_balances: Some(
                    meta.post_token_balances
                        .iter()
                        .map(|tb| TransactionTokenBalance {
                            account_index: tb.account_index as u8,
                            mint: tb.mint.clone(),
                            ui_token_amount: UiTokenAmount {
                                ui_amount: {
                                    let ui_token_amount =
                                        tb.ui_token_amount.clone().unwrap_or_default();
                                    if ui_token_amount.ui_amount == 0.0 {
                                        None
                                    } else {
                                        Some(ui_token_amount.ui_amount)
                                    }
                                },
                                decimals: tb.ui_token_amount.clone().unwrap_or_default().decimals
                                    as u8,
                                amount: tb.ui_token_amount.clone().unwrap_or_default().amount,
                                ui_amount_string: tb
                                    .ui_token_amount
                                    .clone()
                                    .unwrap_or_default()
                                    .ui_amount_string,
                            },
                            owner: tb.clone().owner,
                            program_id: tb.clone().program_id,
                        })
                        .collect(),
                ),
                rewards: Some(
                    meta.rewards
                        .iter()
                        .map(|r| Reward {
                            pubkey: r.clone().pubkey,
                            lamports: r.lamports,
                            post_balance: r.post_balance,
                            reward_type: match r.reward_type {
                                0 => Some(RewardType::Fee),
                                1 => Some(RewardType::Rent),
                                2 => Some(RewardType::Staking),
                                3 => Some(RewardType::Voting),
                                _ => None,
                            },
                            commission: None, // 简化处理，避免类型推断问题
                        })
                        .collect::<Vec<_>>(),
                ),
                loaded_addresses: LoadedAddresses {
                    writable: meta
                        .loaded_writable_addresses
                        .iter()
                        .map(|addr| {
                            Pubkey::new_from_array(
                                addr.clone()
                                    .try_into()
                                    .expect("Failed to convert writable address"),
                            )
                        })
                        .collect(),
                    readonly: meta
                        .loaded_readonly_addresses
                        .iter()
                        .map(|addr| {
                            Pubkey::new_from_array(
                                addr.clone()
                                    .try_into()
                                    .expect("Failed to convert readonly address"),
                            )
                        })
                        .collect(),
                },
                return_data: meta
                    .return_data
                    .as_ref()
                    .map(|return_data| TransactionReturnData {
                        program_id: Pubkey::new_from_array(
                            return_data
                                .program_id
                                .clone()
                                .try_into()
                                .expect("Failed to convert return data program_id"),
                        ),
                        data: return_data.data.clone(),
                    }),
                compute_units_consumed: meta.compute_units_consumed,
            },
        }),
        block_time: Some(block_time),
    };

    Ok(confirmed_txn_with_meta)
}

/// 从交易中提取事件数据
///
/// 解析交易日志消息，提取并解码 PumpFun AMM 事件数据
///
/// # 参数
/// * `confirmed_txn` - 已确认的交易数据
///
/// # 返回值
/// * `Ok(Option<DecodedEvent>)` - 成功提取的事件数据（如果有）
/// * `Err(anyhow::Error)` - 提取过程中发生错误
fn extract_event_from_transaction(
    confirmed_txn: &ConfirmedTransactionWithStatusMeta,
) -> anyhow::Result<Option<DecodedEvent>> {
    use crate::event_account_mapper::{decode_event_data, extract_log_message};
    use base64::Engine;

    if let TransactionWithStatusMeta::Complete(versioned_meta) = &confirmed_txn.tx_with_meta {
        if let Some(logs) = &versioned_meta.meta.log_messages {
            if let Some(data_msg) = extract_log_message(logs) {
                match base64::engine::general_purpose::STANDARD.decode(&data_msg) {
                    Ok(decoded_bytes) => match decode_event_data(&decoded_bytes) {
                        Ok(event) => return Ok(Some(event)),
                        Err(err) => {
                            error!("Failed to decode event data: {}", err.message);
                            return Ok(None);
                        }
                    },
                    Err(err) => {
                        error!("Failed to decode base64 log message: {}", err);
                        return Ok(None);
                    }
                }
            }
        }
    }
    Ok(None)
}

/// 获取交易的编译指令列表
///
/// # 参数
/// * `confirmed_txn` - 已确认的交易数据
///
/// # 返回值
/// * `Vec<TransactionInstructionWithParent>` - 编译指令列表
fn get_compiled_instructions(
    confirmed_txn: &ConfirmedTransactionWithStatusMeta,
) -> Vec<TransactionInstructionWithParent> {
    match &confirmed_txn.tx_with_meta {
        TransactionWithStatusMeta::Complete(versioned_tx_with_meta) => {
            flatten_compiled_instructions(versioned_tx_with_meta)
        }
        TransactionWithStatusMeta::MissingMetadata(_) => vec![],
    }
}

/// 获取交易的内部指令列表
///
/// # 参数
/// * `confirmed_txn` - 已确认的交易数据
///
/// # 返回值
/// * `Vec<TransactionInstructionWithParent>` - 内部指令列表
fn get_inner_instructions(
    confirmed_txn: &ConfirmedTransactionWithStatusMeta,
) -> Vec<TransactionInstructionWithParent> {
    match &confirmed_txn.tx_with_meta {
        TransactionWithStatusMeta::Complete(versioned_tx_with_meta) => {
            flatten_inner_instructions(versioned_tx_with_meta)
        }
        TransactionWithStatusMeta::MissingMetadata(_) => vec![],
    }
}

/// 解码指令列表
///
/// 将原始指令数据解码为可读的指令结构，支持 PumpFun AMM 和 Token 程序
///
/// # 参数
/// * `instructions` - 原始指令列表
/// * `decoded_event` - 关联的事件数据（可选）
///
/// # 返回值
/// * `Ok(Vec<DecodedInstruction>)` - 解码后的指令列表
/// * `Err(anyhow::Error)` - 解码过程中发生错误
fn decode_instructions(
    instructions: &[TransactionInstructionWithParent],
    decoded_event: &Option<DecodedEvent>,
) -> anyhow::Result<Vec<DecodedInstruction>> {
    use crate::instruction_account_mapper::{Idl, InstructionAccountMapper};
    use crate::token_serializable::convert_to_serializable;
    use pumpfun_amm_interface::instructions::PumpfunAmmProgramIx;
    use spl_token::instruction::TokenInstruction;
    use std::{fs, str::FromStr};

    // 读取 IDL 文件
    let token_idl_json = fs::read_to_string("idls/token_program_idl.json")
        .map_err(|e| anyhow::anyhow!("Unable to read Token IDL JSON file: {}", e))?;
    let pumpfun_amm_idl = fs::read_to_string("idls/pump_amm_0.1.0.json")
        .map_err(|e| anyhow::anyhow!("Unable to read PumpFun AMM IDL JSON file: {}", e))?;

    let mut decoded_instructions: Vec<DecodedInstruction> = Vec::new();

    for instruction in instructions {
        // 处理 PumpFun AMM 程序指令
        if instruction.instruction.program_id == Pubkey::from_str(PUMPFUN_AMM_PROGRAM_ID)? {
            if let Ok(decoded_ix) = PumpfunAmmProgramIx::deserialize(&instruction.instruction.data)
            {
                let idl: Idl = serde_json::from_str(&pumpfun_amm_idl)?;
                if let Ok(mapped_accounts) = idl.map_accounts(
                    &instruction.instruction.accounts,
                    &decoded_ix.name().to_string(),
                ) {
                    let decoded_instruction = DecodedInstruction {
                        name: decoded_ix.name().to_string(),
                        accounts: mapped_accounts,
                        data: serde_json::to_value(decoded_ix)?,
                        event: decoded_event.clone(),
                        program_id: instruction.instruction.program_id,
                        parent_program_id: instruction.parent_program_id,
                    };
                    decoded_instructions.push(decoded_instruction);
                }
            }
        }
        // 处理 Token 程序指令
        else if instruction.instruction.program_id == Pubkey::from_str(TOKEN_PROGRAM_ID)? {
            if let Ok(decoded_ix) = TokenInstruction::unpack(&instruction.instruction.data) {
                let ix_name = get_instruction_name_with_typename(&decoded_ix);
                let serializable_ix = convert_to_serializable(decoded_ix);
                let token_idl: Idl = serde_json::from_str(&token_idl_json)?;

                if let Ok(mapped_accounts) =
                    token_idl.map_accounts(&instruction.instruction.accounts, &ix_name)
                {
                    let decoded_instruction = DecodedInstruction {
                        name: ix_name,
                        accounts: mapped_accounts,
                        data: serde_json::to_value(serializable_ix)?,
                        event: None,
                        program_id: instruction.instruction.program_id,
                        parent_program_id: instruction.parent_program_id,
                    };
                    decoded_instructions.push(decoded_instruction);
                }
            }
        }
    }

    Ok(decoded_instructions)
}

/// 构建解析后的交易结构
///
/// 将原始交易数据和解码后的指令组合成完整的解析交易结构
///
/// # 参数
/// * `slot` - 交易所在的插槽号
/// * `block_time` - 区块时间戳
/// * `confirmed_txn` - 已确认的交易数据
/// * `decoded_compiled_instructions` - 解码后的编译指令
/// * `decoded_inner_instructions` - 解码后的内部指令
///
/// # 返回值
/// * `Ok(ParsedConfirmedTransactionWithStatusMeta)` - 构建成功的解析交易
/// * `Err(anyhow::Error)` - 构建过程中发生错误
fn build_parsed_transaction(
    slot: u64,
    block_time: i64,
    confirmed_txn: &ConfirmedTransactionWithStatusMeta,
    decoded_compiled_instructions: Vec<DecodedInstruction>,
    decoded_inner_instructions: Vec<DecodedInstruction>,
) -> anyhow::Result<ParsedConfirmedTransactionWithStatusMeta> {
    let parsed_txn = ParsedConfirmedTransactionWithStatusMeta {
        slot,
        transaction: match &confirmed_txn.tx_with_meta {
            TransactionWithStatusMeta::Complete(versioned_tx_with_meta) => ParsedTransaction {
                signatures: versioned_tx_with_meta.transaction.signatures.clone(),
                message: match &versioned_tx_with_meta.transaction.message {
                    VersionedMessage::V0(msg) => ParsedMessage {
                        header: msg.header.clone(),
                        account_keys: msg.account_keys.clone(),
                        recent_blockhash: msg.recent_blockhash.clone(),
                        instructions: decoded_compiled_instructions,
                        address_table_lookups: msg.address_table_lookups.clone(),
                    },
                    VersionedMessage::Legacy(msg) => ParsedMessage {
                        header: msg.header.clone(),
                        account_keys: msg.account_keys.clone(),
                        recent_blockhash: msg.recent_blockhash.clone(),
                        instructions: decoded_compiled_instructions,
                        address_table_lookups: vec![],
                    },
                },
            },
            _ => return Err(anyhow::anyhow!("Expected Complete transaction variant")),
        },
        meta: match &confirmed_txn.tx_with_meta {
            TransactionWithStatusMeta::Complete(versioned_tx_with_meta) => {
                ParsedTransactionStatusMeta {
                    status: versioned_tx_with_meta.meta.status.clone(),
                    fee: versioned_tx_with_meta.meta.fee,
                    pre_balances: versioned_tx_with_meta.meta.pre_balances.clone(),
                    post_balances: versioned_tx_with_meta.meta.post_balances.clone(),
                    inner_instructions: decoded_inner_instructions,
                    log_messages: versioned_tx_with_meta.meta.log_messages.clone(),
                    pre_token_balances: versioned_tx_with_meta.meta.pre_token_balances.clone(),
                    post_token_balances: versioned_tx_with_meta.meta.post_token_balances.clone(),
                    rewards: versioned_tx_with_meta.meta.rewards.clone(),
                    loaded_addresses: versioned_tx_with_meta.meta.loaded_addresses.clone(),
                    return_data: versioned_tx_with_meta.meta.return_data.clone(),
                    compute_units_consumed: versioned_tx_with_meta.meta.compute_units_consumed,
                }
            }
            _ => return Err(anyhow::anyhow!("Expected Complete transaction variant")),
        },
        block_time: Some(block_time),
    };

    Ok(parsed_txn)
}

fn flatten_transaction_response(
    transaction_with_meta: &VersionedTransactionWithStatusMeta,
) -> Vec<TransactionInstructionWithParent> {
    let mut result = Vec::new();

    let transaction = transaction_with_meta.transaction.clone();
    let ci_ixs = transaction.message.instructions();
    let parsed_accounts = parse_transaction_accounts(
        &transaction.message,
        transaction_with_meta.meta.loaded_addresses.clone(),
    );

    let ordered_cii = match &transaction_with_meta.meta.inner_instructions {
        Some(cii) => {
            let mut cii = cii.clone();
            cii.sort_by(|a, b| a.index.cmp(&b.index));
            cii
        }
        _ => Vec::new(),
    };

    let total_calls: usize = ordered_cii
        .iter()
        .fold(ci_ixs.len(), |acc, cii| acc + cii.instructions.len());

    let mut last_pushed_ix = -1;
    let mut call_index: isize = -1;

    for cii in ordered_cii.iter() {
        while last_pushed_ix != cii.index as i64 {
            last_pushed_ix += 1;
            call_index += 1;
            let ci_ix = &ci_ixs[last_pushed_ix as usize];
            result.push(TransactionInstructionWithParent {
                instruction: compiled_instruction_to_instruction(ci_ix, parsed_accounts.clone()),
                parent_program_id: None,
            });
        }

        for cii_entry in &cii.instructions {
            let parent_program_id =
                parsed_accounts[ci_ixs[last_pushed_ix as usize].program_id_index as usize].pubkey;

            let ix = CompiledInstruction {
                program_id_index: cii_entry.instruction.program_id_index,
                accounts: cii_entry.instruction.accounts.clone(),
                data: cii_entry.instruction.data.clone(),
            };
            result.push(TransactionInstructionWithParent {
                instruction: compiled_instruction_to_instruction(&ix, parsed_accounts.clone()),
                parent_program_id: Some(parent_program_id),
            });

            call_index += 1;
        }
    }

    while call_index < (total_calls - 1).try_into().unwrap() {
        last_pushed_ix += 1;
        call_index += 1;
        let ci_ix = &ci_ixs[last_pushed_ix as usize];
        result.push(TransactionInstructionWithParent {
            instruction: compiled_instruction_to_instruction(ci_ix, parsed_accounts.clone()),
            parent_program_id: None,
        });
    }

    result
}

/// 展开交易的编译指令
///
/// 将交易中的编译指令转换为带有父程序信息的指令列表。
/// 编译指令是交易的主要指令，直接由用户或客户端程序调用。
///
/// # 参数
/// * `transaction_with_meta` - 包含完整元数据的版本化交易
///
/// # 返回值
/// * `Vec<TransactionInstructionWithParent>` - 转换后的指令列表，每个指令包含：
///   - `instruction`: 完整的指令数据（程序ID、账户、数据）
///   - `parent_program_id`: None（编译指令没有父程序）
///
/// # 工作原理
/// 1. 解析交易账户，包括静态账户和通过地址查找表加载的账户
/// 2. 遍历所有编译指令
/// 3. 将每个编译指令转换为完整的指令格式
/// 4. 设置 parent_program_id 为 None，因为这些是顶级指令
fn flatten_compiled_instructions(
    transaction_with_meta: &VersionedTransactionWithStatusMeta,
) -> Vec<TransactionInstructionWithParent> {
    let mut compiled_result = Vec::new();
    let transaction = &transaction_with_meta.transaction;
    let ci_ixs = transaction.message.instructions();
    let parsed_accounts = parse_transaction_accounts(
        &transaction.message,
        transaction_with_meta.meta.loaded_addresses.clone(),
    );

    for ci_ix in ci_ixs {
        compiled_result.push(TransactionInstructionWithParent {
            instruction: compiled_instruction_to_instruction(&ci_ix, parsed_accounts.clone()),
            parent_program_id: None, // 编译指令没有父程序
        });
    }

    compiled_result
}

/// 展开交易的内部指令
///
/// 将交易中的内部指令转换为带有父程序信息的指令列表。
/// 内部指令是由编译指令调用的其他程序指令，形成程序间调用链。
///
/// # 参数
/// * `transaction_with_meta` - 包含完整元数据的版本化交易
///
/// # 返回值
/// * `Vec<TransactionInstructionWithParent>` - 转换后的内部指令列表，每个指令包含：
///   - `instruction`: 完整的指令数据（程序ID、账户、数据）
///   - `parent_program_id`: Some(Pubkey) - 调用此指令的父程序ID
///
/// # 工作原理
/// 1. 解析交易账户，包括静态账户和通过地址查找表加载的账户
/// 2. 获取内部指令列表并按索引排序，确保执行顺序正确
/// 3. 对于每个内部指令组：
///    - 确定父程序ID（调用这些内部指令的编译指令的程序ID）
///    - 转换每个内部指令为完整格式
///    - 设置正确的父程序ID以跟踪调用关系
///
/// # 注意事项
/// - 内部指令的执行顺序很重要，因此需要按索引排序
/// - 父程序ID帮助理解程序间的调用关系和数据流
fn flatten_inner_instructions(
    transaction_with_meta: &VersionedTransactionWithStatusMeta,
) -> Vec<TransactionInstructionWithParent> {
    let mut inner_result = Vec::new();
    let transaction = &transaction_with_meta.transaction;
    let ci_ixs = transaction.message.instructions();
    let parsed_accounts = parse_transaction_accounts(
        &transaction.message,
        transaction_with_meta.meta.loaded_addresses.clone(),
    );

    if let Some(inner_ixs) = &transaction_with_meta.meta.inner_instructions {
        // 按索引排序以确保正确的执行顺序
        let mut ordered_cii = inner_ixs.clone();
        ordered_cii.sort_by(|a, b| a.index.cmp(&b.index));

        for cii in ordered_cii {
            // 获取调用这些内部指令的父程序ID
            let parent_program_id =
                parsed_accounts[ci_ixs[cii.index as usize].program_id_index as usize].pubkey;

            // 处理该组内的所有内部指令
            for cii_entry in cii.instructions {
                let ix = CompiledInstruction {
                    program_id_index: cii_entry.instruction.program_id_index,
                    accounts: cii_entry.instruction.accounts.clone(),
                    data: cii_entry.instruction.data.clone(),
                };
                inner_result.push(TransactionInstructionWithParent {
                    instruction: compiled_instruction_to_instruction(&ix, parsed_accounts.clone()),
                    parent_program_id: Some(parent_program_id), // 设置父程序ID
                });
            }
        }
    }

    inner_result
}

fn compiled_instruction_to_instruction(
    ci: &CompiledInstruction,
    parsed_accounts: Vec<AccountMeta>,
) -> Instruction {
    let program_id = parsed_accounts[ci.program_id_index as usize].pubkey;
    let accounts: Vec<AccountMeta> = ci.accounts.iter().map(|&index| {
        if index as usize >= parsed_accounts.len() {
            panic!(
                "Trying to resolve account at index {} while parsedAccounts is only {}. \
                Looks like you're trying to parse versioned transaction, make sure that LoadedAddresses are passed to the \
                parseTransactionAccounts function",
                index, parsed_accounts.len()
            );
        }
        parsed_accounts[index as usize].clone()
    }).collect();

    Instruction {
        program_id,
        accounts,
        data: ci.data.clone(),
    }
}

/// 解析交易账户信息
///
/// 将版本化消息中的账户信息转换为完整的账户元数据列表，
/// 包括静态账户和通过地址查找表加载的账户。
///
/// # 参数
/// * `message` - 版本化交易消息，包含账户键和头部信息
/// * `loaded_addresses` - 通过地址查找表加载的额外账户地址
///
/// # 返回值
/// * `Vec<AccountMeta>` - 完整的账户元数据列表，每个账户包含：
///   - `pubkey`: 账户的公钥地址
///   - `is_signer`: 是否为交易签名者
///   - `is_writable`: 是否可写入
///
/// # 账户分类和权限计算
///
/// ## 静态账户（按顺序排列）：
/// 1. **签名者账户**：前 `num_required_signatures` 个账户
///    - 可写签名者：前 `num_required_signatures - num_readonly_signed_accounts` 个
///    - 只读签名者：剩余的签名者账户
///
/// 2. **非签名者账户**：剩余的静态账户
///    - 可写非签名者：除了最后 `num_readonly_unsigned_accounts` 个之外的所有非签名者
///    - 只读非签名者：最后 `num_readonly_unsigned_accounts` 个账户
///
/// ## 动态加载账户：
/// 3. **可写加载账户**：通过地址查找表加载的可写账户
/// 4. **只读加载账户**：通过地址查找表加载的只读账户
///
/// # 使用场景
/// - 指令解析：将指令中的账户索引转换为实际的账户信息
/// - 权限验证：确定账户的读写和签名权限
/// - 地址查找表支持：处理 v0 交易中的动态账户加载
pub fn parse_transaction_accounts(
    message: &VersionedMessage,
    loaded_addresses: LoadedAddresses,
) -> Vec<AccountMeta> {
    let accounts = message.static_account_keys();
    let readonly_signed_accounts_count = message.header().num_readonly_signed_accounts as usize;
    let readonly_unsigned_accounts_count = message.header().num_readonly_unsigned_accounts as usize;
    let required_signatures_accounts_count = message.header().num_required_signatures as usize;
    let total_accounts = accounts.len();

    // 处理静态账户
    let mut parsed_accounts: Vec<AccountMeta> = accounts
        .iter()
        .enumerate()
        .map(|(index, pubkey)| {
            // 计算账户是否可写
            let is_writable = index
                < required_signatures_accounts_count - readonly_signed_accounts_count  // 可写签名者
                || (index >= required_signatures_accounts_count  // 或者是可写非签名者
                    && index < total_accounts - readonly_unsigned_accounts_count);

            AccountMeta {
                pubkey: *pubkey,
                is_signer: index < required_signatures_accounts_count, // 前N个是签名者
                is_writable,
            }
        })
        .collect();

    // 添加通过地址查找表加载的可写账户
    parsed_accounts.extend(
        loaded_addresses
            .writable
            .into_iter()
            .map(|pubkey| AccountMeta {
                pubkey,
                is_signer: false, // 加载的账户不能是签名者
                is_writable: true,
            }),
    );

    // 添加通过地址查找表加载的只读账户
    parsed_accounts.extend(
        loaded_addresses
            .readonly
            .into_iter()
            .map(|pubkey| AccountMeta {
                pubkey,
                is_signer: false, // 加载的账户不能是签名者
                is_writable: false,
            }),
    );

    parsed_accounts
}
/// 解析交换交易输出
///
/// 从解析后的交易数据中提取 PumpFun AMM 交换操作的关键信息，
/// 包括代币储备量、价格计算等
///
/// # 参数
/// * `txn` - 解析后的确认交易数据，包含完整的交易信息和元数据
/// * `inner_instructions` - 解码后的内部指令列表，包含程序间调用的指令
/// * `compiled_instructions` - 解码后的编译指令列表，包含交易的主要指令
///
/// # 返回值
/// * `Some(PumpAmmSwapOutput)` - 成功解析的交换输出数据，包含：
///   - `base_mint`: 基础代币的铸币地址
///   - `quote_mint`: 报价代币的铸币地址（通常是 SOL）
///   - `pool_base_token_reserve`: 流动性池中基础代币的储备量
///   - `pool_quote_token_reserve`: 流动性池中报价代币的储备量
///   - `price`: 计算出的代币价格（以报价代币计价）
/// * `None` - 如果交易不是有效的交换操作或解析失败
///
/// # 工作原理
/// 1. 从交易的代币余额变化中识别代币类型和精度
/// 2. 查找 "Buy" 或 "Sell" 指令以确定交换类型
/// 3. 从事件数据中提取流动性池储备量信息
/// 4. 根据代币类型计算正确的价格比率
/// 5. 格式化输出数据，包含可读的储备量和价格信息
fn parse_swap_transaction_output(
    txn: ParsedConfirmedTransactionWithStatusMeta,
    inner_instructions: Vec<DecodedInstruction>,
    compiled_instructions: Vec<DecodedInstruction>,
) -> Option<PumpAmmSwapOutput> {
    const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
    let price: f64;
    let base_reserve_pool: String;
    let quote_reserve_pool: String;
    let decimal = txn
        .meta
        .pre_token_balances
        .as_ref()?
        .iter()
        .find(|balance| balance.mint != SOL_MINT)
        .map(|balance| balance.ui_token_amount.decimals)
        .unwrap_or(9);
    let parsed_instruction = inner_instructions
        .iter()
        .chain(compiled_instructions.iter())
        .find(|ix| ix.name == "Buy" || ix.name == "Sell")?;
    let parsed_event = inner_instructions
        .get(0)
        .and_then(|ix| ix.event.clone())
        .or_else(|| compiled_instructions.get(0).and_then(|ix| ix.event.clone()))?;
    let base_mint = parsed_instruction
        .accounts
        .iter()
        .find(|acc| acc.name == "base_mint")
        .map(|acc| acc.pubkey.to_string())?;
    let quote_mint = parsed_instruction
        .accounts
        .iter()
        .find(|acc| acc.name == "quote_mint")
        .map(|acc| acc.pubkey.to_string())?;
    let (base_reserve, quote_reserve) = match parsed_event {
        DecodedEvent::BuyEvent(event) => (
            event.pool_base_token_reserves,
            event.pool_quote_token_reserves,
        ),
        DecodedEvent::SellEvent(event) => (
            event.pool_base_token_reserves,
            event.pool_quote_token_reserves,
        ),
        _ => return None,
    };
    if base_mint == SOL_MINT {
        base_reserve_pool = (base_reserve as f64 / 1_000_000_000f64).to_string() + " SOL";
        quote_reserve_pool = (quote_reserve as f64 / 10f64.powi(decimal as i32)).to_string();
        price = calculate_pump_amm_price(base_reserve, quote_reserve, decimal);
    } else {
        base_reserve_pool = (base_reserve as f64 / 10f64.powi(decimal as i32)).to_string();
        quote_reserve_pool = (quote_reserve as f64 / 1_000_000_000f64).to_string() + " SOL";
        price = calculate_pump_amm_price(quote_reserve, base_reserve, decimal);
    }
    Some(PumpAmmSwapOutput {
        base_mint,
        quote_mint,
        pool_base_token_reserve: base_reserve_pool,
        pool_quote_token_reserve: quote_reserve_pool,
        price: price.to_string() + " SOL",
    })
}

/// 计算 PumpFun AMM 代币价格
///
/// 根据流动性池中的代币储备量计算代币的当前价格。
/// 使用简单的比率公式：价格 = 基础代币储备量 / 报价代币储备量
///
/// # 参数
/// * `pool_base_reserve` - 流动性池中基础代币的储备量（原始单位，未调整精度）
/// * `pool_quote_reserve` - 流动性池中报价代币的储备量（原始单位，未调整精度）
/// * `decimal` - 报价代币的小数位数，用于精度调整
///
/// # 返回值
/// * `f64` - 计算出的代币价格，表示每个报价代币单位可以购买多少基础代币
///
/// # 计算逻辑
/// 1. 将基础代币储备量从 lamports 转换为 SOL（除以 10^9）
/// 2. 将报价代币储备量根据其精度进行调整（除以 10^decimal）
/// 3. 计算比率：base / quote，得到以报价代币计价的基础代币价格
///
/// # 示例
/// ```
/// // 假设池中有 1000 SOL (1000 * 10^9 lamports) 和 1,000,000 个代币 (1,000,000 * 10^6 最小单位)
/// let price = calculate_pump_amm_price(1000_000_000_000, 1_000_000_000_000, 6);
/// // 结果：1.0 SOL per token
/// ```
fn calculate_pump_amm_price(pool_base_reserve: u64, pool_quote_reserve: u64, decimal: u8) -> f64 {
    // 将基础代币储备量从 lamports 转换为 SOL
    let base = pool_base_reserve as f64 / 1_000_000_000f64;
    // 将报价代币储备量根据精度调整
    let quote = pool_quote_reserve as f64 / 10f64.powi(decimal as i32);
    // 计算价格比率
    base / quote
}
