// 导入 Solana 公钥类型
use solana_sdk::pubkey::Pubkey;

/// 序列化 Pubkey 为字符串
///
/// 这是一个自定义的序列化函数，用于将 Solana 公钥序列化为 Base58 编码的字符串。
/// 通常与 serde 的 `serialize_with` 属性一起使用。
///
/// # 参数
/// * `value` - 要序列化的公钥引用
/// * `serializer` - serde 序列化器
///
/// # 返回值
/// * `Ok(S::Ok)` - 序列化成功
/// * `Err(S::Error)` - 序列化失败
///
/// # 示例
/// ```rust
/// #[derive(Serialize)]
/// struct MyStruct {
///     #[serde(serialize_with = "serialize_pubkey")]
///     pubkey: Pubkey,
/// }
/// ```
pub fn serialize_pubkey<S>(value: &Pubkey, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    // 将公钥转换为 Base58 字符串并序列化
    serializer.serialize_str(&value.to_string())
}

/// 序列化可选的 Pubkey 为字符串或 null
///
/// 这是一个自定义的序列化函数，用于将可选的 Solana 公钥序列化。
/// 如果公钥存在，则序列化为 Base58 编码的字符串；如果为 None，则序列化为 null。
///
/// # 参数
/// * `value` - 要序列化的可选公钥引用
/// * `serializer` - serde 序列化器
///
/// # 返回值
/// * `Ok(S::Ok)` - 序列化成功
/// * `Err(S::Error)` - 序列化失败
///
/// # 示例
/// ```rust
/// #[derive(Serialize)]
/// struct MyStruct {
///     #[serde(serialize_with = "serialize_option_pubkey")]
///     optional_pubkey: Option<Pubkey>,
/// }
/// ```
pub fn serialize_option_pubkey<S>(value: &Option<Pubkey>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    match value {
        // 如果公钥存在，序列化为字符串
        Some(pubkey) => serializer.serialize_str(&pubkey.to_string()),
        // 如果公钥不存在，序列化为 null
        None => serializer.serialize_none(),
    }
}
