// Base64 编解码引擎
use base64::engine::general_purpose;
use base64::Engine;
// 序列化支持
use serde::Serialize;
// Solana 公钥类型
use solana_program::pubkey::Pubkey;

// 导入 PumpFun AMM 接口中的所有事件类型和判别器
use pumpfun_amm_interface::events::{
    // 购买事件相关
    BuyEvent, BuyEventEvent, BUY_EVENT_EVENT_DISCM,
    // 收取代币创建者费用事件相关
    CollectCoinCreatorFeeEvent, CollectCoinCreatorFeeEventEvent, COLLECT_COIN_CREATOR_FEE_EVENT_DISCM,
    // 创建池事件相关
    CreatePoolEvent, CreatePoolEventEvent, CREATE_POOL_EVENT_EVENT_DISCM,
    // 存款事件相关
    DepositEvent, DepositEventEvent, DEPOSIT_EVENT_EVENT_DISCM,
    // 禁用功能事件相关
    DisableEvent, DisableEventEvent, DISABLE_EVENT_EVENT_DISCM,
    // 扩展账户事件相关
    ExtendAccountEvent, ExtendAccountEventEvent, EXTEND_ACCOUNT_EVENT_EVENT_DISCM,
    // 出售事件相关
    SellEvent, SellEventEvent, SELL_EVENT_EVENT_DISCM,
    // 设置联合曲线代币创建者事件相关
    SetBondingCurveCoinCreatorEvent, SetBondingCurveCoinCreatorEventEvent, SET_BONDING_CURVE_COIN_CREATOR_EVENT_DISCM,
    // 设置 Metaplex 代币创建者事件相关
    SetMetaplexCoinCreatorEvent, SetMetaplexCoinCreatorEventEvent, SET_METAPLEX_COIN_CREATOR_EVENT_DISCM,
    // 更新管理员事件相关
    UpdateAdminEvent, UpdateAdminEventEvent, UPDATE_ADMIN_EVENT_EVENT_DISCM,
    // 更新费用配置事件相关
    UpdateFeeConfigEvent, UpdateFeeConfigEventEvent, UPDATE_FEE_CONFIG_EVENT_EVENT_DISCM,
    // 提款事件相关
    WithdrawEvent, WithdrawEventEvent, WITHDRAW_EVENT_EVENT_DISCM
};

/// 解码后的事件枚举
/// 包含所有可能的 PumpFun AMM 事件类型
#[derive(Debug, Clone, Serialize, PartialEq)]
pub enum DecodedEvent {
    /// 购买事件 - 用户购买代币时触发
    BuyEvent(BuyEvent),
    /// 收取代币创建者费用事件 - 代币创建者收取费用时触发
    CollectCoinCreatorFeeEvent(CollectCoinCreatorFeeEvent),
    /// 创建池事件 - 创建新的流动性池时触发
    CreatePoolEvent(CreatePoolEvent),
    /// 存款事件 - 向池子添加流动性时触发
    DepositEvent(DepositEvent),
    /// 禁用功能事件 - 管理员禁用某些功能时触发
    DisableEvent(DisableEvent),
    /// 扩展账户事件 - 扩展账户存储空间时触发
    ExtendAccountEvent(ExtendAccountEvent),
    /// 出售事件 - 用户出售代币时触发
    SellEvent(SellEvent),
    /// 设置联合曲线代币创建者事件 - 从联合曲线设置创建者时触发
    SetBondingCurveCoinCreatorEvent(SetBondingCurveCoinCreatorEvent),
    /// 设置 Metaplex 代币创建者事件 - 从 Metaplex 元数据设置创建者时触发
    SetMetaplexCoinCreatorEvent(SetMetaplexCoinCreatorEvent),
    /// 更新管理员事件 - 更改管理员权限时触发
    UpdateAdminEvent(UpdateAdminEvent),
    /// 更新费用配置事件 - 修改费用参数时触发
    UpdateFeeConfigEvent(UpdateFeeConfigEvent),
    /// 提款事件 - 从池子移除流动性时触发
    WithdrawEvent(WithdrawEvent),
}

/// 账户事件错误结构体
/// 用于表示事件解码过程中的错误
#[derive(Debug)]
pub struct AccountEventError {
    /// 错误消息
    pub message: String,
}

/// 将 Base64 字符串转换为判别器字节数组
///
/// # 参数
/// * `base64_string` - Base64 编码的字符串
///
/// # 返回值
/// * `Ok(Vec<u8>)` - 解码成功的字节数组
/// * `Err(base64::DecodeError)` - 解码失败时的错误
pub fn convert_to_discm(base64_string: &str) -> Result<Vec<u8>, base64::DecodeError> {
    general_purpose::STANDARD.decode(base64_string)
}

/// 从交易日志中提取事件数据消息
///
/// # 参数
/// * `logs` - 交易日志消息列表
///
/// # 返回值
/// * `Some(String)` - 找到的 Base64 编码的事件数据
/// * `None` - 未找到事件数据
///
/// # 工作原理
/// 搜索以 "Program data: " 开头的日志消息，这些消息包含程序发出的事件数据
pub fn extract_log_message(logs: &[String]) -> Option<String> {
    logs.iter()
        .find_map(|message| {
            // 查找包含程序数据的日志消息
            if message.starts_with("Program data: ") {
                // 提取 Base64 编码的数据部分
                let encoded = message.trim_start_matches("Program data: ").trim();
                Some(encoded.to_string())
            } else {
                None
            }
        })
}
pub fn decode_event_data(mut buf: &[u8]) -> Result<DecodedEvent, AccountEventError> {
    if buf.len() < 8 {
        return Err(AccountEventError {
            message: "Buffer too short to contain a valid discriminator.".to_string(),
        });
    }

    let discriminator: [u8; 8] = buf[..8].try_into().expect("Failed to extract first 8 bytes");

    match discriminator {
        BUY_EVENT_EVENT_DISCM => {
            let data = BuyEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize BuyEvent: {}", e),
            })?;
            Ok(DecodedEvent::BuyEvent(data.0))
        }
        COLLECT_COIN_CREATOR_FEE_EVENT_DISCM => {
            let data = CollectCoinCreatorFeeEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize CollectCoinCreatorFeeEvent: {}", e),
            })?;
            Ok(DecodedEvent::CollectCoinCreatorFeeEvent(data.0))
        }
        CREATE_POOL_EVENT_EVENT_DISCM => {
            let data = CreatePoolEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize CreatePoolEvent: {}", e),
            })?;
            Ok(DecodedEvent::CreatePoolEvent(data.0))
        }
        DEPOSIT_EVENT_EVENT_DISCM => {
            let data = DepositEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize DepositEvent: {}", e),
            })?;
            Ok(DecodedEvent::DepositEvent(data.0))
        }
        DISABLE_EVENT_EVENT_DISCM => {
            let data = DisableEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize DisableEvent: {}", e),
            })?;
            Ok(DecodedEvent::DisableEvent(data.0))
        }
        EXTEND_ACCOUNT_EVENT_EVENT_DISCM => {
            let data = ExtendAccountEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize ExtendAccountEvent: {}", e),
            })?;
            Ok(DecodedEvent::ExtendAccountEvent(data.0))
        }
        SELL_EVENT_EVENT_DISCM => {
            let data = SellEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize SellEvent: {}", e),
            })?;
            Ok(DecodedEvent::SellEvent(data.0))
        }
        SET_BONDING_CURVE_COIN_CREATOR_EVENT_DISCM => {
            let data = SetBondingCurveCoinCreatorEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize SetBondingCurveCoinCreatorEvent: {}", e),
            })?;
            Ok(DecodedEvent::SetBondingCurveCoinCreatorEvent(data.0))
        }
        SET_METAPLEX_COIN_CREATOR_EVENT_DISCM => {
            let data = SetMetaplexCoinCreatorEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize SetMetaplexCoinCreatorEvent: {}", e),
            })?;
            Ok(DecodedEvent::SetMetaplexCoinCreatorEvent(data.0))
        }
        UPDATE_ADMIN_EVENT_EVENT_DISCM => {
            let data = UpdateAdminEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize UpdateAdminEvent: {}", e),
            })?;
            Ok(DecodedEvent::UpdateAdminEvent(data.0))
        }
        UPDATE_FEE_CONFIG_EVENT_EVENT_DISCM => {
            let data = UpdateFeeConfigEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize UpdateFeeConfigEvent: {}", e),
            })?;
            Ok(DecodedEvent::UpdateFeeConfigEvent(data.0))
        }
        WITHDRAW_EVENT_EVENT_DISCM => {
            let data = WithdrawEventEvent::deserialize(&mut buf).map_err(|e| AccountEventError {
                message: format!("Failed to deserialize WithdrawEvent: {}", e),
            })?;
            Ok(DecodedEvent::WithdrawEvent(data.0))
        }
        _ => Err(AccountEventError {
            message: "Account discriminator not found.".to_string(),
        }),
    }
}