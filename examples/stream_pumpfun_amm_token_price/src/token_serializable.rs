
// SPL Token 指令序列化模块
// 用于将 SPL Token 指令转换为可序列化的格式，便于 JSON 输出和分析

// 导入 Solana 程序相关类型
use solana_program::{pubkey::Pubkey, program_option::COption};
// 导入 SPL Token 指令类型
use spl_token::instruction::{AuthorityType, TokenInstruction};
// 导入序列化相关 trait
use serde::{Serialize, Deserialize};
// 导入 serde_with 工具，用于自定义序列化
use serde_with::{serde_as, DisplayFromStr};

/// 辅助函数：将 COption<T> 转换为 Option<T>
///
/// COption 是 Solana 程序中使用的可选类型，需要转换为标准的 Option 类型
/// 以便进行序列化。
///
/// # 参数
/// * `coption` - Solana 程序中的可选值
///
/// # 返回值
/// * `Option<T>` - 标准的可选值类型
fn convert_coption<T>(coption: COption<T>) -> Option<T> {
    match coption {
        COption::Some(value) => Some(value),
        COption::None => None,
    }
}

/// 转换设置权限指令的辅助函数
///
/// 将 SPL Token 的权限类型和新权限转换为可序列化的格式
///
/// # 参数
/// * `authority_type` - 权限类型枚举
/// * `new_authority` - 新的权限公钥（可选）
///
/// # 返回值
/// * `SerializableSetAuthority` - 可序列化的设置权限结构体
fn convert_set_authority(
    authority_type: AuthorityType,
    new_authority: COption<Pubkey>,
) -> SerializableSetAuthority {
    SerializableSetAuthority {
        // 将权限类型枚举转换为字符串
        authority_type: match authority_type {
            AuthorityType::MintTokens => "MintTokens".to_string(),
            AuthorityType::FreezeAccount => "FreezeAccount".to_string(),
            AuthorityType::AccountOwner => "AccountOwner".to_string(),
            AuthorityType::CloseAccount => "CloseAccount".to_string(),
        },
        // 将 COption<Pubkey> 转换为 Option<Pubkey>
        new_authority: new_authority.into(),
    }
}


/// 可序列化的初始化铸币指令参数
/// 用于 InitializeMint 和 InitializeMint2 指令
#[serde_as]
#[derive(Serialize, Deserialize)]
pub struct SerializableInitializeMint {
    /// 代币的小数位数
    decimals: u8,
    /// 铸币权限的公钥
    #[serde_as(as = "DisplayFromStr")]
    mint_authority: Pubkey,
    /// 冻结权限的公钥（可选）
    #[serde_as(as = "Option<DisplayFromStr>")]
    freeze_authority: Option<Pubkey>,
}

/// 可序列化的初始化账户3指令参数
/// 用于 InitializeAccount3 指令
#[serde_as]
#[derive(Serialize, Deserialize)]
pub struct SerializableInitializeAccount3 {
    /// 账户所有者的公钥
    #[serde_as(as = "DisplayFromStr")]
    owner: Pubkey,
}

/// 可序列化的设置权限指令参数
/// 用于 SetAuthority 指令
#[serde_as]
#[derive(Serialize, Deserialize)]
pub struct SerializableSetAuthority {
    /// 权限类型（字符串格式）
    authority_type: String,
    /// 新的权限公钥（可选）
    #[serde_as(as = "Option<DisplayFromStr>")]
    new_authority: Option<Pubkey>,
}

/// 可序列化的转账指令参数
/// 用于 Transfer 指令
#[serde_as]
#[derive(Serialize, Deserialize)]
pub struct SerializableTransfer {
    /// 转账金额
    amount: u64,
}

#[serde_as]
#[derive(Serialize, Deserialize)]
pub enum SerializableTokenInstruction {
    InitializeMint(SerializableInitializeMint),
    InitializeAccount,
    InitializeMultisig { m: u8 },
    Transfer(SerializableTransfer),
    Approve { amount: u64 },
    Revoke,
    SetAuthority(SerializableSetAuthority),
    MintTo { amount: u64 },
    Burn { amount: u64 },
    CloseAccount,
    FreezeAccount,
    ThawAccount,
    TransferChecked { amount: u64, decimals: u8 },
    ApproveChecked { amount: u64, decimals: u8 },
    MintToChecked { amount: u64, decimals: u8 },
    BurnChecked { amount: u64, decimals: u8 },
    InitializeAccount2 { #[serde_as(as = "DisplayFromStr")] owner: Pubkey },
    SyncNative,
    InitializeAccount3(SerializableInitializeAccount3),
    InitializeMultisig2 { m: u8 },
    InitializeMint2(SerializableInitializeMint),
    GetAccountDataSize,
    InitializeImmutableOwner,
    AmountToUiAmount { amount: u64 },
    UiAmountToAmount { ui_amount: String },
}


// Convert TokenInstruction to SerializableTokenInstruction
pub fn convert_to_serializable(ix: TokenInstruction) -> SerializableTokenInstruction {
    match ix {
        TokenInstruction::InitializeMint { decimals, mint_authority, freeze_authority } => {
            SerializableTokenInstruction::InitializeMint(SerializableInitializeMint {
                decimals,
                mint_authority,
                freeze_authority: freeze_authority.into(), // Convert COption to Option
            })
        }
        TokenInstruction::InitializeAccount => SerializableTokenInstruction::InitializeAccount,
        TokenInstruction::InitializeMultisig { m } => SerializableTokenInstruction::InitializeMultisig { m },
        TokenInstruction::Transfer { amount } => SerializableTokenInstruction::Transfer(SerializableTransfer { amount }),
        TokenInstruction::Approve { amount } => SerializableTokenInstruction::Approve { amount },
        TokenInstruction::Revoke => SerializableTokenInstruction::Revoke,
        TokenInstruction::SetAuthority { authority_type, new_authority } => {
            SerializableTokenInstruction::SetAuthority(convert_set_authority(authority_type, new_authority))
        }
        TokenInstruction::MintTo { amount } => SerializableTokenInstruction::MintTo { amount },
        TokenInstruction::Burn { amount } => SerializableTokenInstruction::Burn { amount },
        TokenInstruction::CloseAccount => SerializableTokenInstruction::CloseAccount,
        TokenInstruction::FreezeAccount => SerializableTokenInstruction::FreezeAccount,
        TokenInstruction::ThawAccount => SerializableTokenInstruction::ThawAccount,
        TokenInstruction::TransferChecked { amount, decimals } => {
            SerializableTokenInstruction::TransferChecked { amount, decimals }
        }
        TokenInstruction::ApproveChecked { amount, decimals } => {
            SerializableTokenInstruction::ApproveChecked { amount, decimals }
        }
        TokenInstruction::MintToChecked { amount, decimals } => {
            SerializableTokenInstruction::MintToChecked { amount, decimals }
        }
        TokenInstruction::BurnChecked { amount, decimals } => {
            SerializableTokenInstruction::BurnChecked { amount, decimals }
        }
        TokenInstruction::InitializeAccount2 { owner } => {
            SerializableTokenInstruction::InitializeAccount2 { owner }
        }
        TokenInstruction::SyncNative => SerializableTokenInstruction::SyncNative,
        TokenInstruction::InitializeAccount3 { owner } => {
            SerializableTokenInstruction::InitializeAccount3(SerializableInitializeAccount3 { owner })
        }
        TokenInstruction::InitializeMultisig2 { m } => {
            SerializableTokenInstruction::InitializeMultisig2 { m }
        }
        TokenInstruction::InitializeMint2 { decimals, mint_authority, freeze_authority } => {
            SerializableTokenInstruction::InitializeMint2(SerializableInitializeMint {
                decimals,
                mint_authority,
                freeze_authority: freeze_authority.into(),
            })
        }
        TokenInstruction::GetAccountDataSize => SerializableTokenInstruction::GetAccountDataSize,
        TokenInstruction::InitializeImmutableOwner => SerializableTokenInstruction::InitializeImmutableOwner,
        TokenInstruction::AmountToUiAmount { amount } => SerializableTokenInstruction::AmountToUiAmount { amount },
        TokenInstruction::UiAmountToAmount { ui_amount } => {
            SerializableTokenInstruction::UiAmountToAmount { ui_amount: ui_amount.to_string() }
        }
    }
}