{
    // 使用 IntelliSense 了解相关属性。
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'raydium_grpc_txn_stream_n_parse'",
            "cargo": {
                "args": [
                    "build",
                    "--package=raydium_grpc_txn_stream_n_parse"
                ],
                "filter": {
                    "name": "raydium_grpc_txn_stream_n_parse",
                    "kind": "bin"
                }
            },
            "args": [
                "--endpoint",
                // #config# gRPC 端点地址 - 可以修改为其他 Solana Yellowstone gRPC 节点
                "solana-yellowstone-grpc.publicnode.com:443",
                "--proxy",
                // #config# 代理地址 - 如果不需要代理可以删除这两行
                "http://192.168.96.1:7897"
            ],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'raydium_grpc_txn_stream_n_parse'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--package=raydium_grpc_txn_stream_n_parse"
                ],
                "filter": {
                    "name": "raydium_grpc_txn_stream_n_parse",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ]
}